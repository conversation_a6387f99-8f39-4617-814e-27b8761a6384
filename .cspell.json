// cSpell Settings
{
  // Version of the setting file.  Always 0.2
  "version": "0.2",
  // language - current active spelling language
  "language": "en-GB",
  // words - list of words to be always considered correct
  "words": [
    "Authentifi",
    "dialog",
    "frontmatter",
    "onepulse",
    "onupdate",
    "onmove",
    "polyfills",
    "Shiki",
    "tagline",
    "Vetur",
    "Volar",
    "vuejs",
    "fontello",
    "spaciousheight"
  ],
  // flagWords - list of words to be always considered incorrect
  // This is useful for offensive words and common spelling errors.
  // For example "hte" should be "the"
  "flagWords": ["hte"],
  "ignorePaths": [
    "**/node_modules/**",
    "**/dist/**",
    ".cspell.json",
    "**/*.svg",
    "src/icons/config.json"
  ],
  "maxNumberOfProblems": 1
}
