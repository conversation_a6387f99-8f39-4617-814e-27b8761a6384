<script setup lang="ts">
import HelloWorld from './components/HelloWorld.vue'
import TheWelcome from './components/TheWelcome.vue'
import 'onepulse-components/button'
import 'onepulse-components/tabs'
import { ref } from 'vue'

const isButtonCritical = ref(false)
const onButtonClick = () => {
  isButtonCritical.value = !isButtonCritical.value
}

const isTabHeightSpacious = ref(false)
const onTabValueChange = (event: Event) => {
  const customEvent = event as CustomEvent
  const value = customEvent.detail.value as string
  isTabHeightSpacious.value = value === '2'
}
</script>

<template>
  <header>
    <img
      alt="Vue logo"
      class="logo"
      src="./assets/logo.svg"
      width="125"
      height="125"
    />

    <div class="wrapper">
      <HelloWorld msg="You did it!" />
    </div>
  </header>

  <main>
    <TheWelcome />
    <op-button
      @click="onButtonClick()"
      kind="tertiary"
      :critical="isButtonCritical"
      >Click me</op-button
    >
    <op-tab-list
      value="2"
      :spaciousHeight="isTabHeightSpacious"
      @updateValue="onTabValueChange"
    >
      <op-tab value="1">Home</op-tab>
      <op-tab value="2">Profile</op-tab>
      <op-tab value="3">Settings</op-tab>
    </op-tab-list>
  </main>
</template>

<style scoped>
header {
  line-height: 1.5;
}

.logo {
  display: block;
  margin: 0 auto 2rem;
}

@media (min-width: 1024px) {
  header {
    display: flex;
    place-items: center;
    padding-right: calc(var(--section-gap) / 2);
  }

  .logo {
    margin: 0 2rem 0 0;
  }

  header .wrapper {
    display: flex;
    place-items: flex-start;
    flex-wrap: wrap;
  }
}
</style>
