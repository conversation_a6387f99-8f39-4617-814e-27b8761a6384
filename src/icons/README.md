# Onepulse icons

This project contains the source files for the onepulse icons font files. It is generated using the [Fontello](http://fontello.com/) web app.

## Getting started

### Pre-requisites

- [Node.js](https://nodejs.org/en/download/) (LTS version)

### Install dependencies

```bash
npm install
```

### Launch Fontello

```bash
npm start
```

This will open the Fontello web app in your default browser. You can then customize the icons and download the font files.

### Update config

After customizing the icons, you need to update the `config.json` file with the new icon set. You can do this by clicking on the "Download" button in the Fontello web app and replacing the `config.json` file in this project with the downloaded one.

## Usage

Please refer to the Readme.txt file in the downloaded font files for usage instructions.
