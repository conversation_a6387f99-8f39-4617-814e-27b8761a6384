// cSpell:ignore Segoe, Neue
import { css } from 'lit'

export const fontFamily = css`var(--op-font-family, system-ui, system, -apple-system, "San Francisco", Roboto, "Segoe UI", "Helvetica Neue", Helvetica, Arial, sans-serif)`
export const neutralMediumGreyColor = css`var(--neutral-medium-grey-color, #222E37)`
export const lightGreyColorHover = css`var(--light-grey-color-hover, rgb(70 84 99 / 8%))`
export const neutralPaleGreyColor = css`var(--neutral-pale-grey-color, #CED3D9)`
export const neutralPaleGrey15Color = css`var(--neutral-pale-grey-15-color, #F8F8FA)`
export const negativeColorHover = css`var(--alert-negative-color-hover, rgb(227 30 70 / 8%))`
export const primaryColor = css`var(--op-primary-color, #0062E1)`
export const primaryColor2 = css`var(--primary-color, #03A)` // check with design on this duplicate primary color
export const paleGrey25 = css`var(--pale-grey-25, #F4F5F7)`
export const alertPositiveColor = css`var(--alert-positive-color, #008545)`
export const accentColour2 = css`var(--accent-colour-2, #00164E)`
export const darkGreyColor = css`var(--dark-grey-color, #02070D)`
