import { LitElement, css, html } from 'lit'
import { customElement, property } from 'lit/decorators.js'
import { classMap } from 'lit/directives/class-map.js'
import {
  accentColour2,
  fontFamily,
  neutralMediumGreyColor,
} from '../constants.mjs'
import { properties } from '../replaceable'
import { valuePropositionShieldIcon } from '../assets/value-proposition-shield-icon.mjs'

const { title, description, variant } = properties

type VariantType = 'white' | 'blue'

@customElement('op-value-proposition')
export class OpValueProposition extends LitElement {
  @property({ type: String })
  [title] = '';

  @property({ type: String })
  [description] = '';

  @property({ type: String })
  [variant]: VariantType = 'white'

  render() {
    const classes = {
      'value-proposition': true,
      'blue-variant': this[variant] === 'blue',
    }

    return html`
      <div class="${classMap(classes)}">
        <div class="icon-container">
          <div class="shield-background">${valuePropositionShieldIcon}</div>
          <div class="custom-icon">
            <slot name="icon"></slot>
          </div>
        </div>
        <div class="content">
          <h3 class="title">${this[title]}</h3>
          <p class="description">${this[description]}</p>
        </div>
      </div>
    `
  }

  static styles = css`
    :host {
      font-family: ${fontFamily};
      display: block;
    }

    .value-proposition {
      display: flex;
      flex-direction: column;
      align-items: center;
      background-color: white;
      border-radius: 16px;
      box-shadow: 0px 4px 12px rgba(0, 0, 0, 0.05);
      width: 100%;
      max-width: 350px;
      margin: 0 auto;
      position: relative;
      padding-top: 55px;
    }

    .value-proposition.blue-variant {
      background-color: ${accentColour2};
    }

    .icon-container {
      position: absolute;
      top: -80px;
      left: 50%;
      transform: translateX(-50%);
      display: flex;
      justify-content: center;
      height: 130px;
      overflow: visible;
      width: 130px;
    }

    .shield-background {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      z-index: 1;
      display: flex;
      justify-content: center;
      align-items: center;
    }

    .custom-icon {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      z-index: 2;
      display: flex;
      justify-content: center;
      align-items: center;
      width: 32px;
      height: 32px;
      color: white;
    }

    .content {
      text-align: center;
      width: 95%;
      padding-bottom: 24px;
    }

    .title {
      font-size: 20px;
      font-weight: 600;
      color: ${neutralMediumGreyColor};
      margin: 0 0 12px;
    }

    .description {
      font-size: 16px;
      line-height: 1.5;
      color: ${neutralMediumGreyColor};
      margin: 0;
    }

    .blue-variant .title,
    .blue-variant .description {
      color: white;
    }
  `
}

declare global {
  interface HTMLElementTagNameMap {
    'op-value-proposition': OpValueProposition
  }
}
