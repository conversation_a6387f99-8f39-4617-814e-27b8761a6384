import { css, html, LitElement } from 'lit'
import { customElement, property } from 'lit/decorators.js'
import { primaryColor2, paleGrey25, fontFamily } from '../constants.mjs'
import { dialogCloseIcon } from '../assets/dialog-close-icon.mjs'
import { classMap } from 'lit/directives/class-map.js'
import { styleMap } from 'lit/directives/style-map.js'
import { properties, events } from '../replaceable'
import { whiteColor } from '../colors.mjs'

const { styles, classes, visible } = properties
const { updateVisible } = events

export type OpDialogUpdateVisibleEvent = CustomEvent<{ visible: boolean }>

@customElement('op-dialog')
export class OpDialog extends LitElement {
  @property({ type: Boolean })
  [visible] = false;

  @property({ type: Object })
  [classes] = {};

  @property({ type: Object })
  [styles] = {}

  render() {
    if (!this[visible]) {
      return html``
    }
    return html`<div class="mask">
      <div
        class="container ${classMap(this[classes])}"
        style="${styleMap(this[styles])}"
      >
        <div class="header">
          <div><slot name="header"></slot></div>
          <div class="close-icon" @click="${this.handleDialogClose}">
            ${dialogCloseIcon}
          </div>
        </div>
        <div class="content">
          <slot name="content"></slot>
        </div>
        <div class="footer">
          <slot name="footer"></slot>
        </div>
      </div>
    </div>`
  }

  handleDialogClose() {
    this[visible] = false
    const event: OpDialogUpdateVisibleEvent = new CustomEvent(updateVisible, {
      detail: { visible: this[visible] },
      bubbles: true,
      composed: true,
    })
    this.dispatchEvent(event)
  }

  static styles = css`
    :host {
      font-family: ${fontFamily};
    }

    .mask {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-color: rgba(2, 7, 13, 0.2);
      z-index: 99;
      display: flex;
      justify-content: center;
      align-items: center;
    }

    .container {
      border-radius: 8px;
      background-color: ${whiteColor};
      box-shadow: 0px 24px 24px 0px rgba(0, 0, 0, 0.36);
      overflow: hidden;
      z-index: 9999;
    }

    .header {
      padding: 14px 16px 14px 24px;
      background-color: ${primaryColor2};
      color: ${whiteColor};
      font-size: 16px;
      font-style: normal;
      font-weight: 500;
      line-height: 130%;
      display: grid;
      grid-template-columns: 1fr auto;
    }

    .close-icon {
      cursor: pointer;
      display: flex;
      align-items: center;
    }

    .content {
      padding: 24px;
    }

    .footer {
      padding: 4px 12px;
      background-color: ${paleGrey25};
    }
  `
}
