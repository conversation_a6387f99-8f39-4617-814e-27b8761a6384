import { LitElement, css, html } from 'lit'
import { customElement, property } from 'lit/decorators.js'
import { classMap } from 'lit/directives/class-map.js'
import { styleMap } from 'lit/directives/style-map.js'
import { fontFamily, neutralMediumGreyColor } from '../constants.mjs'
import { properties } from '../replaceable'
import { lightGreyColor, whiteColor } from '../colors.mjs'

const { title, description, elevated, width, height } = properties

@customElement('op-card')
export class OpCard extends LitElement {
  @property({ type: String })
  [title] = '';

  @property({ type: String })
  [description] = '';

  @property({ type: String })
  [width] = '100%';

  @property({ type: String })
  [height] = 'auto';

  @property({ type: Boolean })
  [elevated] = false

  private _hasIconSlot = false

  firstUpdated() {
    const iconSlot = this.shadowRoot?.querySelector(
      'slot[name="icon"]',
    ) as HTMLSlotElement
    if (iconSlot) {
      iconSlot.addEventListener('slotchange', () => {
        this._hasIconSlot = this._checkSlotHasContent(iconSlot)
        this.requestUpdate()
      })
      this._hasIconSlot = this._checkSlotHasContent(iconSlot)
    }
  }

  private _checkSlotHasContent(slot: HTMLSlotElement): boolean {
    return slot.assignedNodes().some((node) => {
      return (
        node.nodeType !== Node.TEXT_NODE ||
        (node as Text).textContent?.trim() !== ''
      )
    })
  }

  render() {
    const cardClasses = {
      card: true,
      elevated: this[elevated],
    }

    const headerClasses = {
      'card-header': true,
      'with-icon': this._hasIconSlot,
    }

    const hasTitle = this[title] && this[title].trim() !== ''
    const hasDescription = this[description] && this[description].trim() !== ''
    const hasHeaderContent = hasTitle || hasDescription

    return html`
      <div
        class="${classMap(cardClasses)}"
        style="${styleMap({ width: this[width], height: this[height] })}"
      >
        ${hasHeaderContent
          ? html`
              <div class="${classMap(headerClasses)}">
                <div class="icon-container">
                  <slot name="icon"></slot>
                </div>
                <div class="header-content">
                  ${hasTitle
                    ? html`<h3 class="card-title">${this[title]}</h3>`
                    : ''}
                  ${hasDescription
                    ? html`<p class="card-description">${this[description]}</p>`
                    : ''}
                </div>
              </div>
            `
          : ''}
        <div class="card-content">
          <slot></slot>
        </div>
        <div class="card-footer">
          <slot name="footer"></slot>
        </div>
      </div>
    `
  }

  static styles = css`
    :host {
      display: block;
      font-family: ${fontFamily};
    }

    .card {
      background-color: ${whiteColor};
      border-radius: 16px;
      overflow: hidden;
      box-shadow: 0px 1px 3px rgba(0, 0, 0, 0.1);
      display: flex;
      flex-direction: column;
      width: 100%;
      overflow-y: hidden;
      padding: 24px 0 16px 0;
    }

    .card.elevated {
      box-shadow: 0px 4px 12px rgba(0, 0, 0, 0.1);
    }

    .card-header {
      display: grid;
      grid-template-columns: auto 1fr;
      align-items: center;
      padding: 0 24px 12px 24px;
      gap: 16px;
      height: 53px;
    }

    .card-header.with-icon .header-content {
      padding-left: 0;
    }

    .card-header:not(.with-icon) .icon-container {
      display: none;
    }

    .icon-container {
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .icon-container ::slotted(*) {
      width: 40px;
      height: 40px;
    }

    .header-content {
      color: ${neutralMediumGreyColor};
    }

    .card-title {
      margin: 0;
      font-family: ${fontFamily};
      font-weight: 500;
      font-size: 16px;
      line-height: 130%;
      color: ${neutralMediumGreyColor};
    }

    .card-description {
      margin: 0;
      font-family: ${fontFamily};
      font-weight: 400;
      font-size: 12px;
      line-height: 130%;
      color: ${lightGreyColor};
    }

    .card-content {
      color: ${neutralMediumGreyColor};
      flex-grow: 1;
      overflow: hidden;
      display: flex;
      flex-direction: column;
      min-height: 0;
    }

    .card-content ::slotted(*) {
      overflow: hidden;
      flex-shrink: 1;
      min-width: 0;
    }

    .card-footer {
      display: flex;
      overflow: hidden;
    }
  `
}

declare global {
  interface HTMLElementTagNameMap {
    'op-card': OpCard
  }
}
