import { LitElement, css, html, nothing } from 'lit'
import { customElement, property, queryAll } from 'lit/decorators.js'
import { classMap } from 'lit/directives/class-map.js'
import { repeat } from 'lit/directives/repeat.js'
import { fontFamily, neutralMediumGreyColor } from '../constants.mjs'
import { properties, events } from '../replaceable'
import {
  lightGreyColor,
  paleGrey15Color,
  paleGrey25Color,
  paleGreyColor,
  whiteColor,
  secondaryColor,
} from '../colors.mjs'

const { data, striped, bordered, dense, loading, emptyMessage } = properties
const { updateSort, updateSelection } = events

export type OpTableUpdateSortEvent = CustomEvent<{
  column: string
  direction: 'asc' | 'desc' | null
}>

export type OpTableUpdateSelectionEvent = CustomEvent<{
  selectedRows: any[]
}>

interface TableColumn {
  key: string
  label: string
  width?: string
  align?: 'left' | 'center' | 'right'
  sortable?: boolean
  type?: 'text' | 'number' | 'date' | 'custom'
}

@customElement('op-table')
export class OpTable extends LitElement {
  @property({ type: Array })
  [data]: any[] = []

  @property({ type: Boolean })
  [striped] = false

  @property({ type: Boolean })
  [bordered] = false

  @property({ type: Boolean })
  [dense] = false

  @property({ type: Boolean })
  [loading] = false

  @property({ type: String })
  [emptyMessage] = 'No data available'

  @queryAll('op-table-column')
  private columnElements!: NodeListOf<any>

  private columns: TableColumn[] = []
  private sortColumn: string | null = null
  private sortDirection: 'asc' | 'desc' | null = null

  firstUpdated() {
    this.updateColumns()
    
    // Listen for column changes
    const slot = this.shadowRoot?.querySelector('slot')
    if (slot) {
      slot.addEventListener('slotchange', () => {
        this.updateColumns()
        this.requestUpdate()
      })
    }
  }

  private updateColumns() {
    const columnElements = Array.from(this.querySelectorAll('op-table-column'))
    this.columns = columnElements.map((col: any) => ({
      key: col.key || '',
      label: col.label || '',
      width: col.width,
      align: col.align || 'left',
      sortable: col.sortable || false,
      type: col.columnType || 'text',
    }))
  }

  private handleSort(column: string) {
    const col = this.columns.find(c => c.key === column)
    if (!col?.sortable) return

    if (this.sortColumn === column) {
      this.sortDirection = this.sortDirection === 'asc' ? 'desc' : this.sortDirection === 'desc' ? null : 'asc'
    } else {
      this.sortColumn = column
      this.sortDirection = 'asc'
    }

    if (this.sortDirection === null) {
      this.sortColumn = null
    }

    const event: OpTableUpdateSortEvent = new CustomEvent(updateSort, {
      detail: { column, direction: this.sortDirection },
      bubbles: true,
      composed: true,
    })
    this.dispatchEvent(event)
    this.requestUpdate()
  }

  private getSortedData() {
    if (!this.sortColumn || !this.sortDirection) {
      return this[data]
    }

    return [...this[data]].sort((a, b) => {
      const aVal = a[this.sortColumn!]
      const bVal = b[this.sortColumn!]
      
      if (aVal === bVal) return 0
      
      const result = aVal < bVal ? -1 : 1
      return this.sortDirection === 'asc' ? result : -result
    })
  }

  private renderHeader() {
    return html`
      <thead>
        <tr>
          ${this.columns.map(column => html`
            <th 
              class="${classMap({
                sortable: column.sortable,
                sorted: this.sortColumn === column.key,
                'sort-asc': this.sortColumn === column.key && this.sortDirection === 'asc',
                'sort-desc': this.sortColumn === column.key && this.sortDirection === 'desc',
                [`align-${column.align}`]: true,
              })}"
              style="${column.width ? `width: ${column.width}` : ''}"
              @click="${column.sortable ? () => this.handleSort(column.key) : nothing}"
            >
              <slot name="header-${column.key}">
                ${column.label}
              </slot>
              ${column.sortable ? html`
                <span class="sort-indicator">
                  ${this.sortColumn === column.key && this.sortDirection === 'asc' ? '↑' : 
                    this.sortColumn === column.key && this.sortDirection === 'desc' ? '↓' : '↕'}
                </span>
              ` : nothing}
            </th>
          `)}
        </tr>
      </thead>
    `
  }

  private renderBody() {
    const sortedData = this.getSortedData()
    
    if (this[loading]) {
      return html`
        <tbody>
          <tr>
            <td colspan="${this.columns.length}" class="loading-cell">
              <slot name="loading">Loading...</slot>
            </td>
          </tr>
        </tbody>
      `
    }

    if (sortedData.length === 0) {
      return html`
        <tbody>
          <tr>
            <td colspan="${this.columns.length}" class="empty-cell">
              <slot name="empty">${this[emptyMessage]}</slot>
            </td>
          </tr>
        </tbody>
      `
    }

    return html`
      <tbody>
        ${repeat(sortedData, (row, index) => html`
          <tr class="${classMap({ 'striped-row': this[striped] && index % 2 === 1 })}">
            ${this.columns.map(column => html`
              <td class="align-${column.align}">
                <slot name="cell-${column.key}" .row="${row}" .value="${row[column.key]}">
                  ${row[column.key] ?? ''}
                </slot>
              </td>
            `)}
          </tr>
        `)}
      </tbody>
    `
  }

  render() {
    const tableClasses = {
      table: true,
      striped: this[striped],
      bordered: this[bordered],
      dense: this[dense],
      loading: this[loading],
    }

    return html`
      <div class="table-container">
        <slot style="display: none;"></slot>
        <table class="${classMap(tableClasses)}">
          ${this.renderHeader()}
          ${this.renderBody()}
        </table>
      </div>
    `
  }

  static styles = css`
    :host {
      display: block;
      font-family: ${fontFamily};
    }

    .table-container {
      overflow-x: auto;
      border-radius: 8px;
      background-color: ${whiteColor};
    }

    .table {
      width: 100%;
      border-collapse: collapse;
      background-color: ${whiteColor};
      color: ${neutralMediumGreyColor};
    }

    .table.bordered {
      border: 1px solid ${paleGreyColor};
    }

    thead th {
      background-color: ${paleGrey15Color};
      font-weight: 600;
      font-size: 14px;
      line-height: 20px;
      padding: 12px 16px;
      border-bottom: 1px solid ${paleGreyColor};
      text-align: left;
      position: relative;
    }

    .table.dense thead th {
      padding: 8px 12px;
    }

    thead th.sortable {
      cursor: pointer;
      user-select: none;
    }

    thead th.sortable:hover {
      background-color: ${paleGrey25Color};
    }

    .sort-indicator {
      margin-left: 8px;
      color: ${lightGreyColor};
      font-size: 12px;
    }

    thead th.sorted .sort-indicator {
      color: ${secondaryColor};
    }

    tbody td {
      padding: 12px 16px;
      border-bottom: 1px solid ${paleGrey15Color};
      font-size: 14px;
      line-height: 20px;
    }

    .table.dense tbody td {
      padding: 8px 12px;
    }

    .table.bordered tbody td {
      border-right: 1px solid ${paleGrey15Color};
    }

    .table.bordered tbody td:last-child {
      border-right: none;
    }

    .striped-row {
      background-color: ${paleGrey15Color};
    }

    .align-left {
      text-align: left;
    }

    .align-center {
      text-align: center;
    }

    .align-right {
      text-align: right;
    }

    .loading-cell,
    .empty-cell {
      text-align: center;
      padding: 24px;
      color: ${lightGreyColor};
      font-style: italic;
    }

    tbody tr:hover {
      background-color: ${paleGrey15Color};
    }

    .table.striped tbody tr.striped-row:hover {
      background-color: ${paleGrey25Color};
    }

    /* Responsive design */
    @media (max-width: 768px) {
      .table-container {
        font-size: 12px;
      }

      thead th,
      tbody td {
        padding: 8px 12px;
      }

      .table.dense thead th,
      .table.dense tbody td {
        padding: 6px 8px;
      }
    }
  `
}

declare global {
  interface HTMLElementTagNameMap {
    'op-table': OpTable
  }
}
