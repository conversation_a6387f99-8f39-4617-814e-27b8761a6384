import { LitElement, css, html, nothing } from 'lit'
import { customElement, property } from 'lit/decorators.js'
import { classMap } from 'lit/directives/class-map.js'
import { fontFamily } from '../constants.mjs'
import { buttonLoadingIcon } from '../assets/button-loading-icon.mjs'
import { properties } from '../replaceable'
import {
  lightGreyColor,
  negative10Color,
  negative135Color,
  negativeColor,
  paleGrey50Color,
  primary10Color,
  secondary135Color,
  secondaryColor,
  whiteColor,
} from '../colors.mjs'

const { disabled, dense, critical, inverted, kind, loading } = properties

const buttonTypes = ['button', 'submit', 'reset'] as const
type ButtonType = (typeof buttonTypes)[number]
const isButtonType = (type: string): type is ButtonType =>
  buttonTypes.includes(type as ButtonType)

@customElement('op-button')
export class OpButton extends LitElement {
  @property({ type: Boolean })
  [disabled] = false;

  @property({ type: Boolean })
  [dense] = false;

  @property({ type: String })
  [kind] = 'primary';

  @property({ type: Boolean })
  [inverted] = false;

  @property({ type: Boolean })
  [critical] = false;

  @property({ type: String })
  [properties.type]: ButtonType = 'button';

  @property({ type: Boolean })
  [loading] = false

  render() {
    const classes = {
      primary: this[kind] === 'primary',
      secondary: this[kind] === 'secondary',
      tertiary: this[kind] === 'tertiary',
      dense: this[dense],
      inverted: this[inverted],
      critical: this[critical],
      loading: this[loading],
    }
    const type = isButtonType(this[properties.type])
      ? this[properties.type]
      : 'button'

    return html`<button
      ?disabled="${this[disabled]}"
      class="${classMap(classes)}"
      type="${type}"
    >
      ${this[loading] ? buttonLoadingIcon : html`<slot></slot>`}
    </button>`
  }

  static styles = css`
    :host {
      display: inline-block;
      font-family: ${fontFamily};
    }
    button {
      font-weight: 700;
      font-size: 14px;
      line-height: 16px;
      text-align: center;
      color: ${whiteColor};
      background-color: ${secondaryColor};
      border-width: 1px;
      border-style: solid;
      border-color: ${secondaryColor};
      text-transform: uppercase;
      padding-left: 24px;
      padding-right: 24px;
      border-radius: 8px;
      cursor: pointer;
      height: 48px;
      width: inherit;
    }

    button.inverted {
      color: ${secondaryColor};
      background-color: ${whiteColor};
      border-color: ${whiteColor};
    }

    button.inverted.loading .loading-icon-rect {
      fill: ${secondaryColor};
    }

    button.critical {
      background-color: ${negativeColor};
      color: ${whiteColor};
      border-color: ${negativeColor};
    }

    button.secondary {
      background-color: ${whiteColor};
      color: ${secondaryColor};
      border-color: ${secondaryColor};
    }

    button.secondary.loading .loading-icon-rect {
      fill: ${secondaryColor};
    }

    button.secondary.inverted {
      color: ${whiteColor};
      border-color: ${whiteColor};
      background-color: transparent;
    }

    button.secondary.inverted.loading .loading-icon-rect {
      fill: ${whiteColor};
    }

    button.secondary.critical {
      color: ${negativeColor};
      border-color: ${negativeColor};
    }

    button.secondary.critical.loading .loading-icon-rect {
      fill: ${negativeColor};
    }

    button.tertiary {
      background-color: transparent;
      color: ${secondaryColor};
      border-color: transparent;
    }

    button.tertiary.loading .loading-icon-rect {
      fill: ${secondaryColor};
    }

    button.tertiary.inverted {
      background-color: transparent;
      color: ${whiteColor};
      border-color: transparent;
    }

    button.tertiary.inverted.loading .loading-icon-rect {
      fill: ${whiteColor};
    }

    button.tertiary.critical {
      background-color: transparent;
      color: ${negativeColor};
      border-color: transparent;
    }

    button.tertiary.critical.loading .loading-icon-rect {
      fill: ${negativeColor};
    }

    button.dense {
      padding-left: 12px;
      padding-right: 12px;
      height: 36px;
    }

    button:hover {
      background-color: ${secondary135Color};
      color: ${whiteColor};
      border-color: ${secondary135Color};
    }

    button:hover.loading .loading-icon-rect {
      fill: ${whiteColor};
    }

    button.inverted:hover,
    button.tertiary:hover {
      background-color: ${primary10Color};
      color: ${secondaryColor};
      border-color: ${primary10Color};
    }

    button.inverted:hover.loading .loading-icon-rect,
    button.tertiary:hover.loading .loading-icon-rect {
      fill: ${secondaryColor};
    }

    button.critical:hover {
      background-color: ${negative135Color};
      color: ${whiteColor};
      border-color: ${negative135Color};
    }

    button.critical:hover.loading .loading-icon-rect {
      fill: ${whiteColor};
    }

    button.tertiary.critical:hover {
      background-color: ${negative10Color};
      color: ${negativeColor};
      border-color: ${negative10Color};
    }

    button.tertiary.critical:hover.loading .loading-icon-rect {
      fill: ${negativeColor};
    }

    button:disabled {
      background-color: ${paleGrey50Color};
      color: ${lightGreyColor};
      border-color: ${paleGrey50Color};
      cursor: not-allowed;
    }

    button > svg.loading-icon {
      animation: spin 1s linear infinite;
    }

    @keyframes spin {
      100% {
        transform: rotate(360deg);
      }
    }
  `
}

declare global {
  interface HTMLElementTagNameMap {
    'op-button': OpButton
  }
}
