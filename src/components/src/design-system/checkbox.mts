import { css, html, LitElement } from 'lit'
import { customElement, property } from 'lit/decorators.js'
import { classMap } from 'lit/directives/class-map.js'
import {
  fontFamily,
  lightGreyColorHover,
  negativeColorHover,
  neutralMediumGreyColor,
  neutralPaleGreyColor,
} from '../constants.mjs'
import { properties, events } from '../replaceable'
import {
  lightGreyColor,
  negativeColor,
  secondaryColor,
  whiteColor,
} from '../colors.mjs'

const { disabled, error, indeterminate, name, value, checked } = properties
const { updateChecked } = events

export type OpCheckboxCheckedEvent = CustomEvent<{
  value: string
  checked: boolean
}>

@customElement('op-checkbox')
export class OpCheckbox extends LitElement {
  constructor() {
    super()
  }
  @property({ type: String })
  [name] = '';

  @property({ type: Boolean })
  [checked] = false;

  @property({ type: Boolean })
  [disabled] = false;

  @property({ type: Boolean })
  [indeterminate] = false;

  @property({ type: Boolean })
  [error] = false;

  @property({ type: String })
  [value] = ''

  handleClick() {
    this[checked] = !this[checked]

    const event: OpCheckboxCheckedEvent = new CustomEvent(updateChecked, {
      detail: { value: this[value], checked: this[checked] },
      bubbles: true,
      composed: true,
    })

    this.dispatchEvent(event)
  }

  render() {
    const classes = {
      checked: this[checked],
      disabled: this[disabled],
      indeterminate: this[indeterminate],
      error: this[error],
    }
    return html`
      <label class="checkbox-label ${classMap(classes)}">
        <div class="checkbox-container">
          <input
            type="checkbox"
            name="${this[name]}"
            value="${this[value]}"
            ?checked="${this[checked]}"
            ?disabled="${this[disabled]}"
            @click="${this.handleClick}"
          />
        </div>
        <slot></slot>
      </label>
    `
  }

  static styles = [
    css`
      .checkbox-label {
        display: inline-flex;
        align-items: center;
        gap: 5px;
        cursor: pointer;
        color: ${neutralMediumGreyColor};
        font-family: ${fontFamily};
        font-weight: 400;
        font-size: 14px;
        line-height: 130%;
        text-align: center;
      }

      .checkbox-label .checkbox-container {
        width: 40px;
        height: 40px;
        display: flex;
        justify-content: center;
        align-items: center;
        cursor: inherit;
      }

      .checkbox-label .checkbox-container:hover {
        background-color: ${lightGreyColorHover};
        border-radius: 100px;
      }

      .checkbox-label input {
        appearance: none;
        width: 18px;
        height: 18px;
        border-radius: 2px;
        border: 2px solid ${neutralMediumGreyColor};
        background-color: transparent;
      }
      .checkbox-label input:hover {
        cursor: pointer;
      }

      .checkbox-label.checked input {
        background-color: ${secondaryColor};
        border: none;
        cursor: pointer;
      }
      .checkbox-label.checked.error input {
        background-color: ${negativeColor};
      }
      .checkbox-label.checked input::after {
        content: '';
        mask: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"><path d="M10 16.4L6 12.4L7.4 11L10 13.6L16.6 7L18 8.4L10 16.4Z" fill="white"/></svg>')
          no-repeat center / contain;
        background: ${whiteColor};
        display: block;
        width: 100%;
        height: 100%;
        border-radius: 2px;
      }

      .checkbox-label.checked.indeterminate input::after {
        mask: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"><path d="M7 13V11H17V13H7Z" fill="white"/></svg>')
          no-repeat center / contain;
      }

      .checkbox-label.error .checkbox-container:hover {
        background-color: ${negativeColorHover};
      }

      .checkbox-label.error input {
        border: 2px solid ${negativeColor};
      }

      .checkbox-label.disabled {
        cursor: not-allowed;
        color: ${lightGreyColor};
      }

      .checkbox-label.disabled .checkbox-container:hover {
        background-color: transparent;
        border-radius: 0px;
        cursor: not-allowed;
      }

      .checkbox-label.disabled input {
        border: 2px solid ${neutralPaleGreyColor};
        cursor: not-allowed;
      }

      .checkbox-label.checked.disabled input {
        background-color: ${neutralPaleGreyColor};
      }
    `,
  ]
}
