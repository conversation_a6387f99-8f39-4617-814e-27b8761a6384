import { LitElement, PropertyValues, css, html } from 'lit'
import { customElement, property } from 'lit/decorators.js'
import {
  fontFamily,
  neutralMediumGreyColor,
  neutralPaleGrey15Color,
  neutralPaleGreyColor,
} from '../constants.mjs'
import { classMap } from 'lit/directives/class-map.js'
import { properties, events } from '../replaceable'
import { primary10Color, secondaryColor, whiteColor } from '../colors.mjs'

const { value, active, spaciousHeight } = properties
const { updateActive, updateValue } = events

export type OpTabUpdateActiveEvent = CustomEvent<{
  value: string
  active: boolean
}>

export type OpTabListUpdateValueEvent = CustomEvent<{ value: string }>

@customElement('op-tab')
export class OpTab extends LitElement {
  constructor() {
    super()
    this.addEventListener('click', this.handleClick)
  }

  @property({ type: Boolean })
  [active] = false;

  @property({ type: String })
  [value] = ''

  render() {
    const classes = {
      active: this[active],
    }
    return html`<button
      value="${this[value]}"
      class="${classMap(classes)}"
      type="button"
    >
      <slot></slot>
    </button>`
  }

  handleClick() {
    if (this[active]) {
      return
    }

    this[active] = true

    const event: OpTabUpdateActiveEvent = new CustomEvent(updateActive, {
      detail: { value: this[value], active: this[active] },
      bubbles: true,
      composed: true,
    })

    this.dispatchEvent(event)
  }

  static styles = css`
    button {
      font-family: ${fontFamily};
      font-weight: 500;
      font-size: 14px;
      line-height: 18.2px;
      text-align: center;
      color: ${neutralMediumGreyColor};
      background: none;
      height: 100%;
      padding-left: 16px;
      padding-right: 16px;
      border: none;
      cursor: pointer;
    }

    button:hover {
      background-color: ${primary10Color};
    }

    button.active {
      color: ${secondaryColor};
      background-color: ${whiteColor};
      border-bottom: 2px solid ${secondaryColor};
    }
  `
}

@customElement('op-tab-list')
export class OpTabList extends LitElement {
  constructor() {
    super()
    this.addEventListener('updateActive', this.handleChange)
  }

  @property({ type: String })
  [value] = '';

  @property({ type: Boolean })
  [spaciousHeight] = false

  render() {
    const classes = {
      ['spacious-height']: this[spaciousHeight],
    }

    return html`<div class="${classMap(classes)}"><slot></slot></div>`
  }

  updated(changedProperties: PropertyValues): void {
    if (changedProperties.has('value')) {
      this.updateActiveTab()
    }
  }

  handleChange(event: Event) {
    const customEvent = event as CustomEvent
    const target = event.target as HTMLElement
    if (target?.localName !== 'op-tab') {
      return
    }

    customEvent.stopPropagation()

    const { value } = customEvent.detail
    this[properties.value] = value

    this.updateActiveTab()

    const updateValueEvent: OpTabListUpdateValueEvent = new CustomEvent(
      updateValue,
      {
        detail: { value },
        bubbles: true,
        composed: true,
      },
    )

    this.dispatchEvent(updateValueEvent)
  }

  updateActiveTab() {
    this.querySelectorAll('op-tab').forEach((tab) => {
      tab.active = tab.value === this[value]
    })
  }

  static styles = css`
    div {
      display: flex;
      width: 100%;
      background-color: ${neutralPaleGrey15Color};
      border: 1px solid ${neutralPaleGreyColor};
      border-top-left-radius: 12px;
      border-top-right-radius: 12px;
      overflow: hidden;
      height: 48px;
    }

    div.spacious-height {
      height: 64px;
    }
  `
}

declare global {
  interface HTMLElementTagNameMap {
    'op-tab': OpTab
    'op-tab-list': OpTabList
  }
}
