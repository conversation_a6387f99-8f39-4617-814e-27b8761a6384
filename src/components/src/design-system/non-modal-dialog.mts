import { css, html, LitElement } from 'lit'
import { customElement, property, query } from 'lit/decorators.js'
import { fontFamily, neutralMediumGreyColor } from '../constants.mjs'
import { classMap } from 'lit/directives/class-map.js'
import { styleMap } from 'lit/directives/style-map.js'
import { properties, events } from '../replaceable'
import { dialogCloseIcon } from '../assets/dialog-close-icon.mjs'
import { lightGreyColor, whiteColor } from '../colors.mjs'

const { styles, classes, visible, position, movable } = properties
const { updateVisible, move } = events

export type OpNonModalDialogUpdateVisibleEvent = CustomEvent<{
  visible: boolean
}>
export type OpNonModalDialogMoveEvent = CustomEvent<{ x: number; y: number }>

interface StyleInfo {
  [key: string]: string | number
}

@customElement('op-non-modal-dialog')
export class OpNonModalDialog extends LitElement {
  @property({ type: <PERSON>olean })
  [visible] = false;

  @property({ type: Object })
  [classes] = {};

  @property({ type: Object })
  [styles]: StyleInfo = {};

  @property({ type: String })
  [position] = 'center';

  @property({ type: Boolean })
  [movable] = true

  @query('.container')
  private container!: HTMLElement

  private isDragging = false
  private dragStartX = 0
  private dragStartY = 0
  private initialX = 0
  private initialY = 0
  private currentX = 0
  private currentY = 0

  connectedCallback() {
    super.connectedCallback()
    window.addEventListener('mousemove', this.handleMouseMove.bind(this))
    window.addEventListener('mouseup', this.handleMouseUp.bind(this))
    window.addEventListener('touchmove', this.handleTouchMove.bind(this), {
      passive: false,
    })
    window.addEventListener('touchend', this.handleTouchEnd.bind(this))
  }

  disconnectedCallback() {
    super.disconnectedCallback()
    window.removeEventListener('mousemove', this.handleMouseMove.bind(this))
    window.removeEventListener('mouseup', this.handleMouseUp.bind(this))
    window.removeEventListener('touchmove', this.handleTouchMove.bind(this))
    window.removeEventListener('touchend', this.handleTouchEnd.bind(this))
  }

  render() {
    if (!this[visible]) {
      return html``
    }

    const containerClasses = {
      ...this[classes],
      'position-center': this[position] === 'center',
      'position-top-right': this[position] === 'top-right',
      'position-top-left': this[position] === 'top-left',
      'position-bottom-right': this[position] === 'bottom-right',
      'position-bottom-left': this[position] === 'bottom-left',
      'custom-position': this.currentX !== 0 || this.currentY !== 0,
    }

    const headerClasses = {
      header: true,
      movable: this[movable],
    }

    const customStyles: StyleInfo = { ...this[styles] }
    if (this.currentX !== 0 || this.currentY !== 0) {
      customStyles.transform = 'none'
      customStyles.top = `${this.currentY}px`
      customStyles.left = `${this.currentX}px`
    }

    return html`<div
      class="container ${classMap(containerClasses)}"
      style="${styleMap(customStyles)}"
    >
      <div
        class="${classMap(headerClasses)}"
        @mousedown="${this.handleMouseDown}"
        @touchstart="${this.handleTouchStart}"
      >
        <div class="title"><slot name="header"></slot></div>
        <div class="close-icon" @click="${this.handleDialogClose}">
          ${dialogCloseIcon}
        </div>
      </div>
      <div class="content">
        <slot name="content"></slot>
      </div>
      <div class="footer">
        <slot name="footer"></slot>
      </div>
    </div>`
  }

  handleDialogClose() {
    this[visible] = false
    const event: OpNonModalDialogUpdateVisibleEvent = new CustomEvent(
      updateVisible,
      {
        detail: { visible: this[visible] },
        bubbles: true,
        composed: true,
      },
    )
    this.dispatchEvent(event)
  }

  handleMouseDown(e: MouseEvent) {
    if (!this[movable]) return

    if (e.button !== 0) return

    e.preventDefault()
    this.isDragging = true
    this.dragStartX = e.clientX
    this.dragStartY = e.clientY

    const rect = this.container.getBoundingClientRect()
    this.initialX = this.currentX || rect.left
    this.initialY = this.currentY || rect.top

    this.container.classList.add('dragging')
  }

  handleMouseMove(e: MouseEvent) {
    if (!this.isDragging) return

    e.preventDefault()

    const dx = e.clientX - this.dragStartX
    const dy = e.clientY - this.dragStartY

    this.currentX = this.initialX + dx
    this.currentY = this.initialY + dy

    this.requestUpdate()

    this.dispatchMoveEvent()
  }

  handleMouseUp() {
    if (!this.isDragging) return

    this.isDragging = false
    this.container?.classList.remove('dragging')

    this[position] = 'custom'

    this.dispatchMoveEvent()
  }

  handleTouchStart(e: TouchEvent) {
    if (!this[movable] || e.touches.length !== 1) return

    e.preventDefault()
    this.isDragging = true
    this.dragStartX = e.touches[0].clientX
    this.dragStartY = e.touches[0].clientY

    const rect = this.container.getBoundingClientRect()
    this.initialX = this.currentX || rect.left
    this.initialY = this.currentY || rect.top

    this.container.classList.add('dragging')
  }

  handleTouchMove(e: TouchEvent) {
    if (!this.isDragging || e.touches.length !== 1) return

    e.preventDefault()

    const dx = e.touches[0].clientX - this.dragStartX
    const dy = e.touches[0].clientY - this.dragStartY

    this.currentX = this.initialX + dx
    this.currentY = this.initialY + dy

    this.requestUpdate()

    this.dispatchMoveEvent()
  }

  handleTouchEnd() {
    if (!this.isDragging) return

    this.isDragging = false
    this.container?.classList.remove('dragging')

    this[position] = 'custom'

    this.dispatchMoveEvent()
  }

  dispatchMoveEvent() {
    const event: OpNonModalDialogMoveEvent = new CustomEvent(move, {
      detail: { x: this.currentX, y: this.currentY },
      bubbles: true,
      composed: true,
    })
    this.dispatchEvent(event)
  }

  static styles = css`
    :host {
      font-family: ${fontFamily};
    }

    .container {
      border-radius: 16px;
      background-color: ${whiteColor};
      box-shadow: 0px 8px 24px rgba(0, 0, 0, 0.15);
      overflow: hidden;
      z-index: 9999;
      position: fixed;
      max-width: 90vw;
      max-height: 90vh;
      display: flex;
      flex-direction: column;
      transition: box-shadow 0.2s ease;
    }

    .container.dragging {
      box-shadow: 0px 12px 32px rgba(0, 0, 0, 0.2);
      transition: none;
    }

    .position-center {
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
    }

    .position-top-right {
      top: 20px;
      right: 20px;
    }

    .position-top-left {
      top: 20px;
      left: 20px;
    }

    .position-bottom-right {
      bottom: 20px;
      right: 20px;
    }

    .position-bottom-left {
      bottom: 20px;
      left: 20px;
    }

    .custom-position {
      transform: none;
    }

    .header {
      padding: 20px 24px;
      color: ${neutralMediumGreyColor};
      font-size: 18px;
      font-weight: 600;
      line-height: 130%;
      display: grid;
      grid-template-columns: 1fr auto;
      align-items: center;
    }

    .title {
      font-size: 18px;
      font-weight: 600;
    }

    .header.movable {
      cursor: move;
      cursor: grab;
      position: relative;
    }

    .container.dragging .header.movable {
      cursor: grabbing;
    }

    .close-icon {
      cursor: pointer;
      display: flex;
      align-items: center;
      color: ${lightGreyColor};
    }

    .close-icon:hover {
      color: ${neutralMediumGreyColor};
    }

    .content {
      padding: 24px;
      overflow-y: auto;
      flex: 1;
      color: ${neutralMediumGreyColor};
    }

    .footer {
      padding: 16px 24px;
      display: flex;
      justify-content: space-between;
    }
  `
}

declare global {
  interface HTMLElementTagNameMap {
    'op-non-modal-dialog': OpNonModalDialog
  }
}
