import { LitElement, css, html, nothing } from 'lit'
import { customElement, property } from 'lit/decorators.js'
import { classMap } from 'lit/directives/class-map.js'
import {
  fontFamily,
  neutralMediumGreyColor,
  neutralPaleGreyColor,
} from '../constants.mjs'
import { properties, events } from '../replaceable'
import { styleMap } from 'lit/directives/style-map.js'
import {
  lightGreyColor,
  negativeColor,
  paleGrey50Color,
  secondaryColor,
} from '../colors.mjs'

const {
  value,
  placeholder,
  label,
  name,
  disabled,
  required,
  helperText,
  errorMessage,
  error,
  backgroundColor,
  rows,
  cols,
} = properties
const { updateValue } = events

export type OpTextAreaUpdateValueEvent = CustomEvent<{ value: string }>

@customElement('op-textarea')
export class OpTextArea extends LitElement {
  @property({ type: String })
  [value] = '';

  @property({ type: String })
  [placeholder] = '';

  @property({ type: String })
  [label] = '';

  @property({ type: String })
  [name] = '';

  @property({ type: Boolean })
  [disabled] = false;

  @property({ type: Boolean })
  [required] = false;

  @property({ type: String })
  [helperText] = '';

  @property({ type: String })
  [errorMessage] = '';

  @property({ type: Boolean })
  [error] = false;

  @property({ type: String })
  [backgroundColor] = 'transparent';

  @property({ type: Number })
  [rows] = 4;

  @property({ type: Number })
  [cols] = 50

  private handleInput(e: Event) {
    const input = e.target as HTMLTextAreaElement
    this[value] = input.value

    const event: OpTextAreaUpdateValueEvent = new CustomEvent(updateValue, {
      detail: { value: this[value] },
      bubbles: true,
      composed: true,
    })

    this.dispatchEvent(event)
  }

  render() {
    const inputClasses = {
      'input-field': true,
      error: this[error],
      disabled: this[disabled],
    }
    const inputStyles = {
      backgroundColor: this[backgroundColor],
    }

    const containerClasses = {
      'input-container': true,
      'has-error': this[error],
    }

    return html`
      <div class="${classMap(containerClasses)}">
        ${this[label]
          ? html`<label class="input-label" for="input-${this[name]}">
              ${this[label]}${this[required]
                ? html`<span class="required">*</span>`
                : nothing}
            </label>`
          : nothing}
        <textarea
          id="input-${this[name]}"
          class="${classMap(inputClasses)}"
          style="${styleMap(inputStyles)}"
          name="${this[name]}"
          placeholder="${this[placeholder]}"
          ?disabled="${this[disabled]}"
          ?required="${this[required]}"
          .value="${this[value]}"
          @input="${this.handleInput}"
          rows="${this[rows]}"
          cols="${this[cols]}"
        ></textarea>
        ${this[error] && this[errorMessage]
          ? html`<div class="error-message">${this[errorMessage]}</div>`
          : nothing}
        ${!this[error] && this[helperText]
          ? html`<div class="helper-text">${this[helperText]}</div>`
          : nothing}
      </div>
    `
  }

  static styles = css`
    :host {
      display: block;
      font-family: ${fontFamily};
    }

    .input-container {
      display: flex;
      flex-direction: column;
      width: 100%;
      margin-bottom: 16px;
      position: relative;
    }

    .input-label {
      font-size: 12px;
      font-weight: 400;
      line-height: 1.2;
      color: ${lightGreyColor};
      margin-bottom: 4px;
      display: block;
    }

    .required {
      color: inherit;
      margin-left: 2px;
    }

    .input-field {
      width: 100%;
      padding: 8px 12px;
      font-size: 14px;
      line-height: 1.5;
      color: ${neutralMediumGreyColor};
      background-color: transparent;
      border: 1px solid ${neutralPaleGreyColor};
      border-radius: 8px;
      box-sizing: border-box;
      transition: all 0.2s ease;
    }

    .input-field:focus {
      outline: none;
      border-color: ${secondaryColor};
      border-width: 2px;
      padding: 7px 11px;
      background-color: transparent;
    }

    .input-container:focus-within .input-label {
      color: ${secondaryColor};
      font-weight: 500;
    }

    .input-field.error {
      border-color: ${negativeColor};
    }

    .input-container.has-error .input-label,
    .input-container.has-error:focus-within .input-label,
    .input-container.has-error:has(.input-field:not(:placeholder-shown))
      .input-label {
      color: ${negativeColor};
    }

    .input-field.disabled {
      background-color: ${paleGrey50Color};
      color: ${lightGreyColor};
      cursor: not-allowed;
      border-style: dotted;
    }

    .input-container:has(.input-field.disabled) .input-label,
    .input-container:has(.input-field.disabled):focus-within .input-label,
    .input-container:has(.input-field.disabled):has(
        .input-field:not(:placeholder-shown)
      )
      .input-label {
      color: ${lightGreyColor};
    }

    .input-field::placeholder {
      color: ${lightGreyColor};
      opacity: 0.7;
    }

    .error-message {
      font-size: 12px;
      line-height: 1.2;
      color: ${negativeColor};
      margin-top: 4px;
      padding-left: 12px;
    }

    .helper-text {
      font-size: 12px;
      line-height: 1.2;
      color: ${lightGreyColor};
      margin-top: 4px;
      padding-left: 12px;
    }
  `
}

declare global {
  interface HTMLElementTagNameMap {
    'op-textarea': OpTextArea
  }
}
