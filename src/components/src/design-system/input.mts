import { LitElement, css, html, nothing } from 'lit'
import { customElement, property } from 'lit/decorators.js'
import { classMap } from 'lit/directives/class-map.js'
import {
  fontFamily,
  neutralMediumGreyColor,
  neutralPaleGreyColor,
} from '../constants.mjs'
import { properties, events } from '../replaceable'
import { styleMap } from 'lit/directives/style-map.js'
import {
  lightGreyColor,
  negativeColor,
  paleGrey50Color,
  secondaryColor,
} from '../colors.mjs'

const {
  value,
  placeholder,
  label,
  type,
  name,
  disabled,
  required,
  helperText,
  errorMessage,
  error,
  backgroundColor,
} = properties
const { updateValue } = events

export type OpInputUpdateValueEvent = CustomEvent<{ value: string }>

@customElement('op-input')
export class OpInput extends LitElement {
  @property({ type: String })
  [value] = '';

  @property({ type: String })
  [placeholder] = '';

  @property({ type: String })
  [label] = '';

  @property({ type: String })
  [type] = 'text';

  @property({ type: String })
  [name] = '';

  @property({ type: Boolean })
  [disabled] = false;

  @property({ type: Boolean })
  [required] = false;

  @property({ type: String })
  [helperText] = '';

  @property({ type: String })
  [errorMessage] = '';

  @property({ type: Boolean })
  [error] = false;

  @property({ type: String })
  [backgroundColor] = 'transparent'

  private handleInput(e: Event) {
    const input = e.target as HTMLInputElement
    this[value] = input.value

    const event: OpInputUpdateValueEvent = new CustomEvent(updateValue, {
      detail: { value: this[value] },
      bubbles: true,
      composed: true,
    })

    this.dispatchEvent(event)
  }

  render() {
    const inputClasses = {
      'input-field': true,
      error: this[error],
      disabled: this[disabled],
    }
    const inputStyles = {
      backgroundColor: this[backgroundColor],
    }

    const containerClasses = {
      'input-container': true,
      'has-error': this[error],
    }

    return html`
      <div class="${classMap(containerClasses)}">
        ${this[label]
          ? html`<label class="input-label" for="input-${this[name]}">
              ${this[label]}${this[required]
                ? html`<span class="required">*</span>`
                : nothing}
            </label>`
          : nothing}
        <div class="input-wrapper">
          <div class="prefix-slot">
            <slot name="prefix"></slot>
          </div>
          <input
            id="input-${this[name]}"
            class="${classMap(inputClasses)}"
            style="${styleMap(inputStyles)}"
            type="${this[type]}"
            name="${this[name]}"
            placeholder="${this[placeholder]}"
            ?disabled="${this[disabled]}"
            ?required="${this[required]}"
            .value="${this[value]}"
            @input="${this.handleInput}"
          />
          <div class="suffix-slot">
            <slot name="suffix"></slot>
          </div>
        </div>
        ${this[error] && this[errorMessage]
          ? html`<div class="error-message">${this[errorMessage]}</div>`
          : nothing}
        ${!this[error] && this[helperText]
          ? html`<div class="helper-text">${this[helperText]}</div>`
          : nothing}
      </div>
    `
  }

  firstUpdated() {
    const prefixSlot = this.shadowRoot?.querySelector(
      'slot[name="prefix"]',
    ) as HTMLSlotElement | null
    const suffixSlot = this.shadowRoot?.querySelector(
      'slot[name="suffix"]',
    ) as HTMLSlotElement | null

    if (prefixSlot) {
      prefixSlot.addEventListener('slotchange', () => {
        this.updateSlotState(prefixSlot, 'has-prefix')
      })
      this.updateSlotState(prefixSlot, 'has-prefix')
    }

    if (suffixSlot) {
      suffixSlot.addEventListener('slotchange', () => {
        this.updateSlotState(suffixSlot, 'has-suffix')
      })
      this.updateSlotState(suffixSlot, 'has-suffix')
    }
  }

  private updateSlotState(slot: HTMLSlotElement, className: string) {
    const hasContent =
      slot
        .assignedNodes()
        .filter(
          (node) =>
            node.nodeType !== Node.TEXT_NODE ||
            (node as Text).textContent?.trim() !== '',
        ).length > 0

    const input = this.shadowRoot?.querySelector('.input-field')
    if (input) {
      if (hasContent) {
        input.classList.add(className)
      } else {
        input.classList.remove(className)
      }
    }

    if (className === 'has-prefix') {
      const container = this.shadowRoot?.querySelector('.input-container')
      if (container) {
        if (hasContent) {
          container.classList.add(className)
        } else {
          container.classList.remove(className)
        }
      }
    }

    this.requestUpdate()
  }

  static styles = css`
    :host {
      display: block;
      font-family: ${fontFamily};
    }

    .input-container {
      display: flex;
      flex-direction: column;
      width: 100%;
      margin-bottom: 16px;
      position: relative;
    }

    .input-label {
      font-size: 12px;
      font-weight: 400;
      line-height: 1.2;
      color: ${lightGreyColor};
      margin-bottom: 4px;
      display: block;
    }

    .required {
      color: inherit;
      margin-left: 2px;
    }

    .input-wrapper {
      position: relative;
      width: 100%;
      display: flex;
      align-items: center;
    }

    .prefix-slot,
    .suffix-slot {
      position: absolute;
      display: flex;
      align-items: center;
      justify-content: center;
      height: 100%;
      color: ${lightGreyColor};
      z-index: 1;
    }

    .prefix-slot {
      left: 12px;
    }

    .suffix-slot {
      right: 12px;
    }

    .input-field {
      width: 100%;
      height: 48px;
      padding: 8px 12px;
      font-size: 14px;
      line-height: 1.5;
      color: ${neutralMediumGreyColor};
      background-color: transparent;
      border: 1px solid ${neutralPaleGreyColor};
      border-radius: 8px;
      box-sizing: border-box;
      transition: all 0.2s ease;
    }

    .input-field.has-prefix {
      padding-left: 40px;
    }

    .input-field.has-suffix {
      padding-right: 40px;
    }

    .input-field:focus {
      outline: none;
      border-color: ${secondaryColor};
      border-width: 2px;
      padding: 7px 11px;
      background-color: transparent;
    }

    .input-field.has-prefix:focus {
      padding-left: 40px;
    }

    .input-field.has-suffix:focus {
      padding-right: 40px;
    }

    .input-container:focus-within .input-label {
      color: ${secondaryColor};
      font-weight: 500;
    }

    .input-field.error {
      border-color: ${negativeColor};
    }

    .input-container.has-error .input-label,
    .input-container.has-error:focus-within .input-label,
    .input-container.has-error:has(.input-field:not(:placeholder-shown))
      .input-label {
      color: ${negativeColor};
    }

    .input-field.disabled {
      background-color: ${paleGrey50Color};
      color: ${lightGreyColor};
      cursor: not-allowed;
      border-style: dotted;
    }

    .input-container:has(.input-field.disabled) .input-label,
    .input-container:has(.input-field.disabled):focus-within .input-label,
    .input-container:has(.input-field.disabled):has(
        .input-field:not(:placeholder-shown)
      )
      .input-label {
      color: ${lightGreyColor};
    }

    .input-field::placeholder {
      color: ${lightGreyColor};
      opacity: 0.7;
    }

    .error-message {
      font-size: 12px;
      line-height: 1.2;
      color: ${negativeColor};
      margin-top: 4px;
      padding-left: 12px;
    }

    .helper-text {
      font-size: 12px;
      line-height: 1.2;
      color: ${lightGreyColor};
      margin-top: 4px;
      padding-left: 12px;
    }
  `
}

declare global {
  interface HTMLElementTagNameMap {
    'op-input': OpInput
  }
}
