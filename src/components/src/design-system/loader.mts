import { css, html, LitElement, nothing } from 'lit'
import { customElement, property } from 'lit/decorators.js'
import { neutralMediumGreyColor } from '../constants.mjs'
import { properties } from '../replaceable'
import { loaderIcon } from '../assets/loader-icon.mjs'

const { label } = properties

@customElement('op-loader')
export class OpLoader extends LitElement {
  @property({ type: String })
  [label] = ''

  render() {
    return html` <div class="loader-container">
      <div class="loader">
        ${loaderIcon}
        ${this[label]
          ? html`<span class="label">${this[label]}</span>`
          : nothing}
      </div>
    </div>`
  }

  static styles = css`
    .loader-container {
      display: inline-block;
    }

    .loader {
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      height: 100%;
    }

    .loader .label {
      font-size: 16px;
      font-style: normal;
      font-weight: 400;
      line-height: 130%;
      margin-top: 8px;
      color: ${neutralMediumGreyColor};
    }

    .loader svg {
      animation: spin 1s linear infinite;
    }

    @keyframes spin {
      100% {
        transform: rotate(360deg);
      }
    }
  `
}
