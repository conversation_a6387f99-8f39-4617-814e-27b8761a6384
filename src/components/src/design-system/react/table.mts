import React from 'react'
import { createComponent, type EventName } from '@lit/react'
import {
  OpTable as OpTableWC,
  OpTableUpdateSortEvent,
  OpTableUpdateSelectionEvent,
} from '../table.mjs'
import { OpTableColumn as OpTableColumnWC } from '../table-column.mjs'
import { events } from '../../replaceable'

const { updateSort, updateSelection } = events

export const OpTable = createComponent({
  tagName: 'op-table',
  elementClass: OpTableWC,
  react: React,
  events: {
    onupdateSort: updateSort as EventName<OpTableUpdateSortEvent>,
    onupdateSelection: updateSelection as EventName<OpTableUpdateSelectionEvent>,
  },
})

export const OpTableColumn = createComponent({
  tagName: 'op-table-column',
  elementClass: OpTableColumnWC,
  react: React,
})
