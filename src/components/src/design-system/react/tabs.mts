import React from 'react'
import { createComponent, type EventName } from '@lit/react'
import {
  OpTab as OpTabWC,
  OpTabList as OpTabListWC,
  OpTabUpdateActiveEvent,
  OpTabListUpdateValueEvent,
} from '../tabs.mjs'
import { events } from '../../replaceable'

const { updateActive, updateValue } = events

export const OpTab = createComponent({
  tagName: 'op-tab',
  elementClass: OpTabWC,
  react: React,
  events: {
    onupdateActive: updateActive as EventName<OpTabUpdateActiveEvent>,
  },
})

export const OpTabList = createComponent({
  tagName: 'op-tab-list',
  elementClass: OpTabListWC,
  react: React,
  events: {
    onupdateValue: updateValue as EventName<OpTabListUpdateValueEvent>,
  },
})
