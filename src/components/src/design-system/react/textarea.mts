import React from 'react'
import { createComponent, EventName } from '@lit/react'
import { events } from '../../replaceable'
import {
  OpTextArea as OpTextAreaWC,
  OpTextAreaUpdateValueEvent,
} from '../textarea.mjs'

const { updateValue } = events

export const OpTextarea = createComponent({
  tagName: 'op-textarea',
  elementClass: OpTextAreaWC,
  react: React,
  events: {
    onupdateValue: updateValue as EventName<OpTextAreaUpdateValueEvent>,
  },
})
