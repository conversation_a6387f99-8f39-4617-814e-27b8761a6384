import React from 'react'
import { createComponent, EventName } from '@lit/react'
import {
  OpCheckboxCheckedEvent,
  OpCheckbox as OpCheckboxWC,
} from '../checkbox.mjs'
import { events } from '../../replaceable'

const { updateChecked } = events

export const OpCheckbox = createComponent({
  tagName: 'op-checkbox',
  elementClass: OpCheckboxWC,
  react: React,
  events: {
    onupdateChecked: updateChecked as EventName<OpCheckboxCheckedEvent>,
  },
})
