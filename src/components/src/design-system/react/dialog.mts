import React from 'react'
import { createComponent, EventName } from '@lit/react'
import {
  OpDialogUpdateVisibleEvent,
  OpDialog as OpDialogWC,
} from '../dialog.mjs'
import { events } from '../../replaceable'

const { updateVisible } = events

export const OpDialog = createComponent({
  tagName: 'op-dialog',
  elementClass: OpDialogWC,
  react: React,
  events: {
    onupdateVisible: updateVisible as EventName<OpDialogUpdateVisibleEvent>,
  },
})
