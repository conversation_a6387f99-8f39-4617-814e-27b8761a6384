import React from 'react'
import { createComponent, EventName } from '@lit/react'
import { OpInput as OpInputWC, OpInputUpdateValueEvent } from '../input.mjs'
import { events } from '../../replaceable'

const { updateValue } = events

export const OpInput = createComponent({
  tagName: 'op-input',
  elementClass: OpInputWC,
  react: React,
  events: {
    onupdateValue: updateValue as EventName<OpInputUpdateValueEvent>,
  },
})
