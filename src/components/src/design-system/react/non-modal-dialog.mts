import React from 'react'
import { createComponent, EventName } from '@lit/react'
import {
  OpNonModalDialogUpdateVisibleEvent,
  OpNonModalDialogMoveEvent,
  OpNonModalDialog as OpNonModalDialogWC,
} from '../non-modal-dialog.mjs'
import { events } from '../../replaceable'

const { updateVisible, move } = events

export const OpNonModalDialog = createComponent({
  tagName: 'op-non-modal-dialog',
  elementClass: OpNonModalDialogWC,
  react: React,
  events: {
    onupdateVisible:
      updateVisible as EventName<OpNonModalDialogUpdateVisibleEvent>,
    onmove: move as EventName<OpNonModalDialogMoveEvent>,
  },
})
