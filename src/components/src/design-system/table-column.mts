import { LitElement, css, html } from 'lit'
import { customElement, property } from 'lit/decorators.js'
import { properties } from '../replaceable'

const { key, label, width, align, sortable, columnType } = properties

type AlignType = 'left' | 'center' | 'right'
type ColumnType = 'text' | 'number' | 'date' | 'custom'

@customElement('op-table-column')
export class OpTableColumn extends LitElement {
  @property({ type: String })
  [key] = ''

  @property({ type: String })
  [label] = ''

  @property({ type: String })
  [width] = ''

  @property({ type: String })
  [align]: AlignType = 'left'

  @property({ type: Boolean })
  [sortable] = false

  @property({ type: String })
  [columnType]: ColumnType = 'text'

  // This component is used for configuration only and doesn't render anything
  render() {
    return html``
  }

  static styles = css`
    :host {
      display: none;
    }
  `
}

declare global {
  interface HTMLElementTagNameMap {
    'op-table-column': OpTableColumn
  }
}
