import { css } from 'lit'

export const accent1Color = css`var(--op-accent-1-color, #00008C)`
export const accent2Color = css`var(--op-accent-2-color, #00164E)`

export const darkGreyColor = css`var(--op-dark-grey-color, #02070D)`

export const extended01Color = css`var(--op-extended-01-color, #00AF43)`
export const extended02Color = css`var(--op-extended-02-color, #83C74D)`
export const extended03Color = css`var(--op-extended-03-color, #FFC700)`
export const extended04Color = css`var(--op-extended-04-color, #AA0050)`
export const extended05Color = css`var(--op-extended-05-color, #B150C5)`
export const extended06Color = css`var(--op-extended-06-color, #665EC7)`
export const extended07Color = css`var(--op-extended-07-color, #4B32B0)`
export const extended08Color = css`var(--op-extended-08-color, #FF5C00)`
export const extended09Color = css`var(--op-extended-09-color, #0691FF)`
export const extended10Color = css`var(--op-extended-10-color, #23C1FF)`
export const extended11Color = css`var(--op-extended-11-color, #1C8ECF)`
export const extended12Color = css`var(--op-extended-12-color, #006996)`

export const lightGreyColor = css`var(--op-light-grey-color, #465463)`
export const lightGrey80Color = css`var(--op-light-grey-80-color, #697786)`

export const mediumGreyColor = css`var(--op-medium-grey-color, #222E37)`

export const negativeColor = css`var(--op-negative-color, #E31E46)`
export const negative135Color = css`var(--op-negative-135-color, #C00228)`
export const negative25Color = css`var(--op-negative-25-color, #F6C2C2)`
export const negative10Color = css`var(--op-negative-10-color, #FBE6E6)`
export const negative4Color = css`var(--op-negative-4-color, #FEF5F5)`

export const paleGreyColor = css`var(--op-pale-grey-color, #CED3D9)`
export const paleGrey50Color = css`var(--op-pale-grey-50-color, #E3E6EA)`
export const paleGrey25Color = css`var(--op-pale-grey-25-color, #F4F5F7)`
export const paleGrey15Color = css`var(--op-pale-grey-15-color, #F8F8FA)`
export const paleGrey4Color = css`var(--op-pale-grey-4-color, #F6F7F7)`

export const positiveColor = css`var(--op-positive-color, #008545)`
export const positive25Color = css`var(--op-positive-25-color, #BFE0CC)`
export const positive4Color = css`var(--op-positive-4-color, #F5FAF7)`

export const primaryColor = css`var(--op-primary-color, #0033AA)`
export const primary20Color = css`var(--op-primary-20-color, #BFDCFC)`
export const primary10Color = css`var(--op-primary-10-color, #DFEEFD)`
export const primary4Color = css`var(--op-primary-4-color, #F2F6FF)`

export const secondaryColor = css`var(--op-secondary-color, #0051FF)`
export const secondary135Color = css`var(--op-secondary-135-color, #003FCA)`

export const warningColor = css`var(--op-warning-color, #FF8A00)`
export const warning25Color = css`var(--op-warning-25-color, #FFE9C7)`
export const warning4Color = css`var(--op-warning-4-color, #FFFBF6)`

export const whiteColor = css`var(--op-white-color, #FFFFFF)`
