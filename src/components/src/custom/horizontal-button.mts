import { LitElement, css, html, nothing } from 'lit'
import { customElement, property } from 'lit/decorators.js'
import {
  neutralPaleGreyColor,
  fontFamily,
  darkGreyColor,
} from '../constants.mjs'
import { properties, events } from '../replaceable'
import { classMap } from 'lit/directives/class-map.js'
import { styleMap } from 'lit/directives/style-map.js'
import { closeCircleIcon } from '../assets/close-circle-icon.mjs'
import { lightGreyColor, whiteColor } from '../colors.mjs'

const { title, description, closable, classes, styles } = properties
const { close } = events

@customElement('op-horizontal-button')
export class OpHorizontalButton extends LitElement {
  @property({ type: String })
  [title] = '';

  @property({ type: String })
  [description] = '';

  @property({ type: Boolean })
  [closable] = false;

  @property({ type: Object })
  [classes] = {};

  @property({ type: Object })
  [styles] = {}

  handleClose(e: Event) {
    e.stopPropagation()

    const closeEvent = new Event(close, {
      bubbles: true,
      composed: true,
    })
    this.dispatchEvent(closeEvent)
  }

  render() {
    return html` <div
      class="container ${classMap(this[classes])}"
      style="${styleMap(this[styles])}"
    >
      ${this[closable]
        ? html`<span class="close-icon" @click="${this.handleClose}">
            ${closeCircleIcon}
          </span>`
        : nothing}
      <div class="horizontal-button">
        <slot name="icon"></slot>
        ${this[title] ? html`<div class="title">${this[title]}</div>` : nothing}
        ${this[description]
          ? html`<div class="description">${this[description]}</div>`
          : nothing}
      </div>
    </div>`
  }

  static styles = css`
    :host {
      font-family: ${fontFamily};
      display: inline-block;
    }

    .container {
      position: relative;
    }

    .container:hover {
      cursor: pointer;
    }

    .close-icon {
      position: absolute;
      right: 10px;
      top: 10px;
    }

    .horizontal-button {
      display: flex;
      height: 115px;
      padding: 16px;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      gap: 8px;
      flex-shrink: 0;
      align-self: stretch;
      border-radius: 9.885px;
      border: 1px solid ${neutralPaleGreyColor};
      background: ${whiteColor};
      text-align: center;
    }

    .title {
      color: ${darkGreyColor};
      font-size: 14px;
      font-style: normal;
      font-weight: 500;
      line-height: 130%;
    }

    .description {
      color: ${lightGreyColor};
      font-size: 10px;
      font-style: normal;
      font-weight: 400;
      line-height: 130%;
    }
  `
}

declare global {
  interface HTMLElementTagNameMap {
    'op-horizontal-button': OpHorizontalButton
  }
}
