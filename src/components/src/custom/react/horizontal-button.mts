import React from 'react'
import { createComponent, EventName } from '@lit/react'
import { OpHorizontalButton as OpHorizontalButtonWC } from '../horizontal-button.mjs'
import { events } from '../../replaceable'

const { close } = events

export const OpHorizontalButton = createComponent({
  tagName: 'op-horizontal-button',
  elementClass: OpHorizontalButtonWC,
  react: React,
  events: {
    onclose: close as EventName,
  },
})
