import { LitElement, css, html } from 'lit'
import { customElement, property } from 'lit/decorators.js'
import { classMap } from 'lit/directives/class-map.js'
import { styleMap } from 'lit/directives/style-map.js'
import { accentColour2, fontFamily } from '../constants.mjs'
import { properties } from '../replaceable'
import { whiteColor } from '../colors.mjs'

const { title, description, styles, classes } = properties

@customElement('op-detailed-product-card')
export class OpDetailedProductCard extends LitElement {
  @property({ type: String })
  [title] = '';

  @property({ type: String })
  [description] = '';

  @property({ type: Object })
  [styles] = {};

  @property({ type: Object })
  [classes] = {}

  render() {
    const cardClasses = {
      ...this[classes],
      'detailed-product-card': true,
    }

    const hasTitle = this[title] && this[title].trim() !== ''
    const hasDescription = this[description] && this[description].trim() !== ''

    return html`
      <div class="${classMap(cardClasses)}" style="${styleMap(this[styles])}">
        <div class="image-section">
          <slot name="image"></slot>
        </div>
        <div class="content-section">
          <div class="text-content">
            ${hasTitle
              ? html`<h2 class="product-title">${this[title]}</h2>`
              : ''}
            ${hasDescription
              ? html`<p class="product-description">${this[description]}</p>`
              : ''}
          </div>
          <div class="actions-section">
            <slot name="actions"></slot>
          </div>
        </div>
      </div>
    `
  }

  static styles = css`
    :host {
      display: block;
      font-family: ${fontFamily};
    }

    .detailed-product-card {
      display: flex;
      background: ${accentColour2};
      border-radius: 16px;
      overflow: hidden;
      min-height: 376px;
      box-shadow: 0px 4px 12px rgba(0, 0, 0, 0.15);
    }

    .image-section {
      flex: 0 0 45%;
      position: relative;
      overflow: hidden;
    }

    .image-section ::slotted(*) {
      width: 100%;
      height: 100%;
      object-fit: cover;
      display: block;
    }

    .content-section {
      flex: 1;
      padding: 32px;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      color: ${whiteColor};
    }

    .text-content {
      margin-bottom: 24px;
    }

    .product-title {
      margin: 0 0 16px 0;
      font-family: ${fontFamily};
      font-weight: 600;
      font-size: 24px;
      line-height: 32px;
      color: ${whiteColor};
    }

    .product-description {
      margin: 0;
      font-family: ${fontFamily};
      font-weight: 400;
      font-size: 16px;
      line-height: 24px;
      color: ${whiteColor};
      opacity: 0.9;
    }

    .actions-section {
      display: flex;
      gap: 12px;
      align-items: center;
    }

    .actions-section ::slotted(*) {
      flex-shrink: 0;
    }

    /* Responsive design */
    @media (max-width: 768px) {
      .detailed-product-card {
        flex-direction: column;
        min-height: auto;
      }

      .image-section {
        flex: 0 0 200px;
      }

      .content-section {
        padding: 24px;
      }

      .product-title {
        font-size: 20px;
        line-height: 28px;
      }

      .product-description {
        font-size: 14px;
        line-height: 20px;
      }
    }
  `
}

declare global {
  interface HTMLElementTagNameMap {
    'op-detailed-product-card': OpDetailedProductCard
  }
}
