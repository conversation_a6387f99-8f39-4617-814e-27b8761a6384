#!/usr/bin/env tsx

import fs from 'fs'
import path from 'path'

const __dirname = process.cwd()

/**
 * TypeScript script to dynamically generate exports for package.json
 */

interface ComponentInfo {
  name: string
  prefix: string
}

interface ExportConfig {
  types: string
  import: string
  require: string
}

interface PackageExports {
  [key: string]: ExportConfig
}

interface PackageJson {
  exports: PackageExports
  [key: string]: any
}

const COMPONENT_DIRS = [
  { path: 'src/design-system', prefix: 'design-system' },
  { path: 'src/custom', prefix: 'custom' },
] as const

const PACKAGE_JSON_PATH = path.join(__dirname, 'package.json')

/**
 * Get all component files from specified directories
 */
function getComponentFiles(): ComponentInfo[] {
  const components: ComponentInfo[] = []

  COMPONENT_DIRS.forEach(({ path: dir, prefix }) => {
    const fullPath = path.join(__dirname, dir)

    if (!fs.existsSync(fullPath)) {
      console.warn(`Directory ${dir} does not exist, skipping...`)
      return
    }

    const files = fs.readdirSync(fullPath)

    files.forEach((file: string) => {
      // Only process .mts files (component files)
      if (file.endsWith('.mts') && file !== 'index.mts') {
        const componentName = file.replace('.mts', '')
        components.push({ name: componentName, prefix })
      }
    })
  })

  return components.sort((a, b) => a.name.localeCompare(b.name))
}

/**
 * Get all component files from specified directories
 */
function getComponentFiles(): string[] {
  const components = new Set<string>()

  COMPONENT_DIRS.forEach((dir) => {
    const fullPath = path.join(__dirname, '..', dir)

    if (!fs.existsSync(fullPath)) {
      console.warn(`Directory ${dir} does not exist, skipping...`)
      return
    }

    const files = fs.readdirSync(fullPath)

    files.forEach((file) => {
      // Only process .mts files (component files)
      if (file.endsWith('.mts') && file !== 'index.mts') {
        const componentName = file.replace('.mts', '')
        components.add(componentName)
      }
    })
  })

  return Array.from(components).sort()
}

/**
 * Generate export entries for a component
 */
function generateComponentExports(componentName: string): PackageExports {
  const exports: PackageExports = {}

  // Main component export (vanilla)
  exports[`./${componentName}`] = {
    types: `./dist/${componentName}.d.ts`,
    import: `./dist/${componentName}.mjs`,
    require: `./dist/${componentName}.js`,
  }

  // React component export
  exports[`./react/${componentName}`] = {
    types: `./dist/react/${componentName}.d.ts`,
    import: `./dist/react/${componentName}.mjs`,
    require: `./dist/react/${componentName}.js`,
  }

  return exports
}

/**
 * Generate all exports
 */
function generateExports(): PackageExports {
  const components = getComponentFiles()
  const exports: PackageExports = {}

  // Main export
  exports['.'] = {
    types: './dist/index.d.ts',
    import: './dist/index.mjs',
    require: './dist/index.js',
  }

  // Component exports
  components.forEach((component) => {
    const componentExports = generateComponentExports(component)
    Object.assign(exports, componentExports)
  })

  // React main export
  exports['./react'] = {
    types: './dist/react/index.d.ts',
    import: './dist/react/index.mjs',
    require: './dist/react/index.js',
  }

  return exports
}

/**
 * Update package.json with generated exports
 */
function updatePackageJson(): void {
  try {
    // Read current package.json
    const packageJsonContent = fs.readFileSync(PACKAGE_JSON_PATH, 'utf8')
    const packageJson: PackageJson = JSON.parse(packageJsonContent)

    // Generate new exports
    const newExports = generateExports()

    // Update exports
    packageJson.exports = newExports

    // Write back to package.json with proper formatting
    fs.writeFileSync(
      PACKAGE_JSON_PATH,
      JSON.stringify(packageJson, null, 2) + '\n',
    )

    console.log('✅ Successfully updated package.json exports')
    console.log(
      `📦 Generated exports for ${Object.keys(newExports).length} entries`,
    )

    // Log the components found
    const components = getComponentFiles()
    console.log(`🎯 Components found: ${components.join(', ')}`)

    // Show preview of generated exports
    console.log('\n📋 Generated exports:')
    Object.keys(newExports).forEach((key) => {
      console.log(`   - ${key}`)
    })
  } catch (error) {
    console.error(
      '❌ Error updating package.json:',
      error instanceof Error ? error.message : String(error),
    )
    process.exit(1)
  }
}

/**
 * Validate that all export files exist in dist (for post-build validation)
 */
function validateExports(): void {
  const exports = generateExports()
  const distPath = path.join(__dirname, '..', 'dist')

  if (!fs.existsSync(distPath)) {
    console.warn('⚠️  dist directory does not exist, skipping validation')
    return
  }

  const missingFiles: string[] = []

  Object.entries(exports).forEach(([exportPath, config]) => {
    const typesFile = path.join(__dirname, '..', config.types)
    const importFile = path.join(__dirname, '..', config.import)
    const requireFile = path.join(__dirname, '..', config.require)

    if (!fs.existsSync(typesFile)) missingFiles.push(config.types)
    if (!fs.existsSync(importFile)) missingFiles.push(config.import)
    if (!fs.existsSync(requireFile)) missingFiles.push(config.require)
  })

  if (missingFiles.length > 0) {
    console.warn('⚠️  Missing export files:')
    missingFiles.forEach((file) => console.warn(`   - ${file}`))
    process.exit(1)
  } else {
    console.log('✅ All export files exist')
  }
}

/**
 * Generate a preview of what exports would be created without modifying files
 */
function previewExports(): void {
  const exports = generateExports()
  const components = getComponentFiles()

  console.log('🔍 Export Preview')
  console.log('================')
  console.log(`📦 Total exports: ${Object.keys(exports).length}`)
  console.log(`🎯 Components: ${components.length}`)
  console.log(`📂 Component directories: ${COMPONENT_DIRS.join(', ')}`)

  console.log('\n📋 Exports that would be generated:')
  Object.entries(exports).forEach(([key, config]) => {
    console.log(`\n"${key}": {`)
    console.log(`  "types": "${config.types}",`)
    console.log(`  "import": "${config.import}",`)
    console.log(`  "require": "${config.require}"`)
    console.log(`}`)
  })

  console.log(`\n🎯 Components found: ${components.join(', ')}`)
}

// Main execution
const command = process.argv[2]

switch (command) {
  case 'validate':
    validateExports()
    break
  case 'preview':
    previewExports()
    break
  case 'generate':
  default:
    updatePackageJson()
    break
}

export {
  generateExports,
  updatePackageJson,
  validateExports,
  getComponentFiles,
  previewExports,
}
