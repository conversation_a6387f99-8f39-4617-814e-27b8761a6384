#!/usr/bin/env node

const fs = require('fs')
const path = require('path')

/**
 * <PERSON>ript to dynamically generate exports for package.json based on     // Log the components found
    const components = getComponentFiles();
    console.log(`🎯 Components found:`);
    components.forEach(({ name, prefix }) => {
      console.log(`   - ${prefix}/${name}`);
    });component files
 */

const COMPONENT_DIRS = [
  { path: 'src/design-system', prefix: 'design-system' },
  { path: 'src/custom', prefix: 'custom' },
]

const PACKAGE_JSON_PATH = path.join(__dirname, '..', 'package.json')

/**
 * Get all component files from specified directories
 */
function getComponentFiles() {
  const components = []

  COMPONENT_DIRS.forEach(({ path: dir, prefix }) => {
    const fullPath = path.join(__dirname, '..', dir)

    if (!fs.existsSync(fullPath)) {
      console.warn(`Directory ${dir} does not exist, skipping...`)
      return
    }

    const files = fs.readdirSync(fullPath)

    files.forEach((file) => {
      // Only process .mts files (component files)
      if (file.endsWith('.mts') && file !== 'index.mts') {
        const componentName = file.replace('.mts', '')
        components.push({ name: componentName, prefix })
      }
    })
  })

  return components.sort((a, b) => a.name.localeCompare(b.name))
}

/**
 * Generate export entries for a component
 */
function generateComponentExports(component) {
  const exports = {}
  const { name, prefix } = component

  // Main component export (vanilla) - follows folder structure
  exports[`./${prefix}/${name}`] = {
    types: `./dist/${prefix}/${name}.d.ts`,
    import: `./dist/${prefix}/${name}.mjs`,
    require: `./dist/${prefix}/${name}.js`,
  }

  // React component export - nested under the prefix folder
  exports[`./react/${prefix}/${name}`] = {
    types: `./dist/${prefix}/react/${name}.d.ts`,
    import: `./dist/${prefix}/react/${name}.mjs`,
    require: `./dist/${prefix}/react/${name}.js`,
  }

  return exports
}

/**
 * Generate all exports
 */
function generateExports() {
  const components = getComponentFiles()
  const exports = {}

  // Main export
  exports['.'] = {
    types: './dist/index.d.ts',
    import: './dist/index.mjs',
    require: './dist/index.js',
  }

  // Component exports
  components.forEach((component) => {
    const componentExports = generateComponentExports(component)
    Object.assign(exports, componentExports)
  })

  // Add folder-level exports for design-system and custom
  exports['./design-system'] = {
    types: './dist/design-system/index.d.ts',
    import: './dist/design-system/index.mjs',
    require: './dist/design-system/index.js',
  }

  exports['./custom'] = {
    types: './dist/custom/index.d.ts',
    import: './dist/custom/index.mjs',
    require: './dist/custom/index.js',
  }

  // React exports
  exports['./react'] = {
    types: './dist/react/index.d.ts',
    import: './dist/react/index.mjs',
    require: './dist/react/index.js',
  }

  exports['./react/design-system'] = {
    types: './dist/design-system/react/index.d.ts',
    import: './dist/design-system/react/index.mjs',
    require: './dist/design-system/react/index.js',
  }

  exports['./react/custom'] = {
    types: './dist/custom/react/index.d.ts',
    import: './dist/custom/react/index.mjs',
    require: './dist/custom/react/index.js',
  }

  return exports
}

/**
 * Update package.json with generated exports
 */
function updatePackageJson() {
  try {
    // Read current package.json
    const packageJson = JSON.parse(fs.readFileSync(PACKAGE_JSON_PATH, 'utf8'))

    // Generate new exports
    const newExports = generateExports()

    // Update exports
    packageJson.exports = newExports

    // Write back to package.json with proper formatting
    fs.writeFileSync(
      PACKAGE_JSON_PATH,
      JSON.stringify(packageJson, null, 2) + '\n',
    )

    console.log('✅ Successfully updated package.json exports')
    console.log(
      `📦 Generated exports for ${Object.keys(newExports).length} entries`,
    )

    // Log the components found
    const components = getComponentFiles()
    console.log(`🎯 Components found: ${components.join(', ')}`)
  } catch (error) {
    console.error('❌ Error updating package.json:', error.message)
    process.exit(1)
  }
}

/**
 * Validate that all export files exist in dist (for post-build validation)
 */
function validateExports() {
  const exports = generateExports()
  const distPath = path.join(__dirname, '..', 'dist')

  if (!fs.existsSync(distPath)) {
    console.warn('⚠️  dist directory does not exist, skipping validation')
    return
  }

  const missingFiles = []

  Object.entries(exports).forEach(([exportPath, config]) => {
    if (typeof config === 'object' && config.types) {
      const typesFile = path.join(__dirname, '..', config.types)
      const importFile = path.join(__dirname, '..', config.import)
      const requireFile = path.join(__dirname, '..', config.require)

      if (!fs.existsSync(typesFile)) missingFiles.push(config.types)
      if (!fs.existsSync(importFile)) missingFiles.push(config.import)
      if (!fs.existsSync(requireFile)) missingFiles.push(config.require)
    }
  })

  if (missingFiles.length > 0) {
    console.warn('⚠️  Missing export files:')
    missingFiles.forEach((file) => console.warn(`   - ${file}`))
  } else {
    console.log('✅ All export files exist')
  }
}

// Main execution
if (require.main === module) {
  const command = process.argv[2]

  switch (command) {
    case 'validate':
      validateExports()
      break
    case 'generate':
    default:
      updatePackageJson()
      break
  }
}

module.exports = {
  generateExports,
  updatePackageJson,
  validateExports,
  getComponentFiles,
}
