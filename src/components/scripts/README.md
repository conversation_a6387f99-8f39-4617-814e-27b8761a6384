# Export Generation Scripts

This directory contains scripts to automatically generate the `exports` field in `package.json` based on the component files in your project.

## Available Scripts

### 1. JavaScript Version (`generate-exports.js`)

The Node.js version that works without additional dependencies.

```bash
# Generate exports and update package.json
npm run generate-exports

# Or run directly
node scripts/generate-exports.js

# Validate that all export files exist (run after build)
node scripts/generate-exports.js validate
```

### 2. TypeScript Version (`generate-exports.ts`)

The TypeScript version with better type safety and additional features.

```bash
# Generate exports and update package.json
npm run generate-exports:ts

# Preview what exports would be generated (without modifying files)
npm run generate-exports:preview

# Validate that all export files exist (run after build)
npm run validate-exports

# Or run directly with tsx
tsx scripts/generate-exports.ts
tsx scripts/generate-exports.ts preview
tsx scripts/generate-exports.ts validate
```

## How It Works

1. **Scans Component Directories**: The script looks for `.mts` files in:

   - `src/design-system/`
   - `src/custom/`

2. **Generates Export Patterns**: For each component found (e.g., `button.mts`), it creates:

   - Vanilla export: `./button`
   - React export: `./react/button`

3. **Updates package.json**: Replaces the `exports` field with the generated exports

4. **Maintains Standard Exports**: Always includes:
   - Main export: `.`
   - React main export: `./react`

## Export Structure

Each component generates exports in this format:

```json
{
  "./component-name": {
    "types": "./dist/component-name.d.ts",
    "import": "./dist/component-name.mjs",
    "require": "./dist/component-name.js"
  },
  "./react/component-name": {
    "types": "./dist/react/component-name.d.ts",
    "import": "./dist/react/component-name.mjs",
    "require": "./dist/react/component-name.js"
  }
}
```

## Automated Usage

The script is automatically run before builds:

```bash
npm run build  # Runs generate-exports first via prebuild hook
```

## Commands Overview

| Command                            | Description                             |
| ---------------------------------- | --------------------------------------- |
| `npm run generate-exports`         | Generate exports using JS version       |
| `npm run generate-exports:ts`      | Generate exports using TS version       |
| `npm run generate-exports:preview` | Preview exports without modifying files |
| `npm run validate-exports`         | Validate that all export files exist    |

## Troubleshooting

### Missing Components

If a component isn't showing up in exports:

1. Ensure the file has a `.mts` extension
2. Check that it's in one of the scanned directories
3. Run preview mode to see what's being detected

### Build Errors

If build fails due to missing exports:

1. Run `npm run validate-exports` to see which files are missing
2. Ensure your build process generates all expected output files
3. Check that component files are properly structured

### Adding New Component Directories

To scan additional directories, edit the `COMPONENT_DIRS` array in the script:

```javascript
const COMPONENT_DIRS = [
  'src/design-system',
  'src/custom',
  'src/new-directory', // Add new directories here
]
```
