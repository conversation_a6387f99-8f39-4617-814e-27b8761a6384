# Changelog

All notable changes to this project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.1.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [Unreleased]

### Added

- Background property to input component [(#54)](https://github.com/standardbank-cibbt/onepulse-components/pull/54)
- Textarea component for design system version 3.1 [(#56)](https://github.com/standardbank-cibbt/onepulse-components/pull/56)

### Changed

- Project export structure to include `src/custom/` and `src/design-system/` directories [(#52)](https://github.com/standardbank-cibbt/onepulse-components/pull/52)

### Fixed

- Card component padding and margin issues [(#53)](https://github.com/standardbank-cibbt/onepulse-components/pull/53)
- Button component styles to inherit width [(#55)](https://github.com/standardbank-cibbt/onepulse-components/pull/55)

## [1.5.2] - 2025-06-12

### Fixed

- Correct card content colour and button height [(49)] (https://github.com/standardbank-cibbt/onepulse-components/pull/49)

## [1.5.1] - 2025-06-10

### Fixed

- Correct button colour and card footer justification [(48)] (https://github.com/standardbank-cibbt/onepulse-components/pull/48)

## [1.5.0] - 2025-06-06

### Added

- Card component for design system version 3.1 [(#42)](https://github.com/standardbank-cibbt/onepulse-components/pull/42)
- Detailed product card component for design system version 3.1 [(#44)](https://github.com/standardbank-cibbt/onepulse-components/pull/44)
- Horizontal card component [(#45)](https://github.com/standardbank-cibbt/onepulse-components/pull/45)

## [1.4.0] - 2025-05-22

### Added

- Non-modal dialog component for design system version 3.1 [(#32)](https://github.com/standardbank-cibbt/onepulse-components/pull/32)
- Input component for design system version 3.1 [(#30)](https://github.com/standardbank-cibbt/onepulse-components/pull/30)
- Value proposition component for design system version 3.1 [(#37)](https://github.com/standardbank-cibbt/onepulse-components/pull/37)
- Checkbox component for design system version 3.1 [(#38)](https://github.com/standardbank-cibbt/onepulse-components/pull/38)

### Fixed

- Dialog mask layer issue due to low z-index [(#41)](https://github.com/standardbank-cibbt/onepulse-components/pull/41)

## [1.3.1] - 2025-04-28

### Fixed

- Z-index issue with the Dialog component that caused it to hide other elements [(#33)](https://github.com/standardbank-cibbt/onepulse-components/pull/33)

## [1.3.0] - 2025-03-13

### Added

- Loader component [(#24)](https://github.com/standardbank-cibbt/onepulse-components/pull/24)

## [1.2.0] - 2025-02-09

### Added

- Dialog component for design system version 3.1 [(#21)](https://github.com/standardbank-cibbt/onepulse-components/pull/21)
- Tab components for design system version 3.1 [(#17)](https://github.com/standardbank-cibbt/onepulse-components/pull/18)

## [1.1.0] - 2025-01-25

### Added

- Button component loading state [(#16)](https://github.com/standardbank-cibbt/onepulse-components/pull/16)

## [1.0.0] - 2025-01-24

### Added

- Button component for design system version 3.1 [(#1)](https://github.com/standardbank-cibbt/onepulse-components/pull/1),[#5](https://github.com/standardbank-cibbt/onepulse-components/pull/5),[#8](https://github.com/standardbank-cibbt/onepulse-components/pull/8)
