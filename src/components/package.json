{"name": "onepulse-components", "version": "1.5.2", "description": "Framework agnostic UI components", "main": "dist/index.js", "module": "dist/index.mjs", "types": "dist/index.d.ts", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.js"}, "./design-system/button": {"types": "./dist/design-system/button.d.ts", "import": "./dist/design-system/button.mjs", "require": "./dist/design-system/button.js"}, "./react/design-system/button": {"types": "./dist/design-system/react/button.d.ts", "import": "./dist/design-system/react/button.mjs", "require": "./dist/design-system/react/button.js"}, "./design-system/card": {"types": "./dist/design-system/card.d.ts", "import": "./dist/design-system/card.mjs", "require": "./dist/design-system/card.js"}, "./react/design-system/card": {"types": "./dist/design-system/react/card.d.ts", "import": "./dist/design-system/react/card.mjs", "require": "./dist/design-system/react/card.js"}, "./design-system/checkbox": {"types": "./dist/design-system/checkbox.d.ts", "import": "./dist/design-system/checkbox.mjs", "require": "./dist/design-system/checkbox.js"}, "./react/design-system/checkbox": {"types": "./dist/design-system/react/checkbox.d.ts", "import": "./dist/design-system/react/checkbox.mjs", "require": "./dist/design-system/react/checkbox.js"}, "./custom/detailed-product-card": {"types": "./dist/custom/detailed-product-card.d.ts", "import": "./dist/custom/detailed-product-card.mjs", "require": "./dist/custom/detailed-product-card.js"}, "./react/custom/detailed-product-card": {"types": "./dist/custom/react/detailed-product-card.d.ts", "import": "./dist/custom/react/detailed-product-card.mjs", "require": "./dist/custom/react/detailed-product-card.js"}, "./design-system/dialog": {"types": "./dist/design-system/dialog.d.ts", "import": "./dist/design-system/dialog.mjs", "require": "./dist/design-system/dialog.js"}, "./react/design-system/dialog": {"types": "./dist/design-system/react/dialog.d.ts", "import": "./dist/design-system/react/dialog.mjs", "require": "./dist/design-system/react/dialog.js"}, "./custom/horizontal-button": {"types": "./dist/custom/horizontal-button.d.ts", "import": "./dist/custom/horizontal-button.mjs", "require": "./dist/custom/horizontal-button.js"}, "./react/custom/horizontal-button": {"types": "./dist/custom/react/horizontal-button.d.ts", "import": "./dist/custom/react/horizontal-button.mjs", "require": "./dist/custom/react/horizontal-button.js"}, "./design-system/input": {"types": "./dist/design-system/input.d.ts", "import": "./dist/design-system/input.mjs", "require": "./dist/design-system/input.js"}, "./react/design-system/input": {"types": "./dist/design-system/react/input.d.ts", "import": "./dist/design-system/react/input.mjs", "require": "./dist/design-system/react/input.js"}, "./design-system/loader": {"types": "./dist/design-system/loader.d.ts", "import": "./dist/design-system/loader.mjs", "require": "./dist/design-system/loader.js"}, "./react/design-system/loader": {"types": "./dist/design-system/react/loader.d.ts", "import": "./dist/design-system/react/loader.mjs", "require": "./dist/design-system/react/loader.js"}, "./design-system/non-modal-dialog": {"types": "./dist/design-system/non-modal-dialog.d.ts", "import": "./dist/design-system/non-modal-dialog.mjs", "require": "./dist/design-system/non-modal-dialog.js"}, "./react/design-system/non-modal-dialog": {"types": "./dist/design-system/react/non-modal-dialog.d.ts", "import": "./dist/design-system/react/non-modal-dialog.mjs", "require": "./dist/design-system/react/non-modal-dialog.js"}, "./design-system/tabs": {"types": "./dist/design-system/tabs.d.ts", "import": "./dist/design-system/tabs.mjs", "require": "./dist/design-system/tabs.js"}, "./react/design-system/tabs": {"types": "./dist/design-system/react/tabs.d.ts", "import": "./dist/design-system/react/tabs.mjs", "require": "./dist/design-system/react/tabs.js"}, "./design-system/textarea": {"types": "./dist/design-system/textarea.d.ts", "import": "./dist/design-system/textarea.mjs", "require": "./dist/design-system/textarea.js"}, "./react/design-system/textarea": {"types": "./dist/design-system/react/textarea.d.ts", "import": "./dist/design-system/react/textarea.mjs", "require": "./dist/design-system/react/textarea.js"}, "./design-system/value-proposition": {"types": "./dist/design-system/value-proposition.d.ts", "import": "./dist/design-system/value-proposition.mjs", "require": "./dist/design-system/value-proposition.js"}, "./react/design-system/value-proposition": {"types": "./dist/design-system/react/value-proposition.d.ts", "import": "./dist/design-system/react/value-proposition.mjs", "require": "./dist/design-system/react/value-proposition.js"}, "./design-system": {"types": "./dist/design-system/index.d.ts", "import": "./dist/design-system/index.mjs", "require": "./dist/design-system/index.js"}, "./custom": {"types": "./dist/custom/index.d.ts", "import": "./dist/custom/index.mjs", "require": "./dist/custom/index.js"}, "./react": {"types": "./dist/react/index.d.ts", "import": "./dist/react/index.mjs", "require": "./dist/react/index.js"}, "./react/design-system": {"types": "./dist/design-system/react/index.d.ts", "import": "./dist/design-system/react/index.mjs", "require": "./dist/design-system/react/index.js"}, "./react/custom": {"types": "./dist/custom/react/index.d.ts", "import": "./dist/custom/react/index.mjs", "require": "./dist/custom/react/index.js"}}, "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "dev": "vite", "build": "tsup", "build:bundled": "npm run build && rollup -c", "generate-exports": "node scripts/generate-exports.js", "generate-exports:ts": "tsx scripts/generate-exports.ts", "generate-exports:preview": "tsx scripts/generate-exports.ts preview", "validate-exports": "tsx scripts/generate-exports.ts validate", "prebuild": "npm run generate-exports"}, "author": "<EMAIL>", "license": "ISC", "dependencies": {"@lit/react": "^1.0.7", "lit": "^3.2.1"}, "devDependencies": {"@rollup/plugin-node-resolve": "^16.0.0", "@rollup/plugin-replace": "^6.0.2", "@rollup/plugin-terser": "^0.4.4", "rollup": "^4.29.1", "rollup-plugin-summary": "^3.0.0", "tsup": "^8.3.5", "tsx": "^4.7.0", "typescript": "^5.7.2", "vite": "^6.1.6"}, "peerDependencies": {"react": "^19.0.0"}, "peerDependenciesMeta": {"react": {"optional": true}}}