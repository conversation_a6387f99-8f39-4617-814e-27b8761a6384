<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Vite + Lit + TS</title>
    <script type="module" src="/src/index.mts"></script>
  </head>
  <body style="background-color: #f5f5f5">
    <div style="max-width: 800px; margin: 0 auto; padding: 20px">
      <h2>OnePulse Components Demo</h2>

      <h3>Button</h3>
      <op-button kind="tertiary" loading critical>Click me</op-button>

      <h3>Tabs</h3>
      <op-tab-list value="2" spaciousHeight>
        <op-tab value="1">Home</op-tab>
        <op-tab value="2">Profile</op-tab>
        <op-tab value="3">Settings</op-tab>
      </op-tab-list>

      <h3>Input Fields</h3>
      <div
        style="display: flex; flex-wrap: wrap; gap: 16px; margin-bottom: 24px"
      >
        <op-input
          label="Label"
          placeholder="Populated"
          name="mat-input-1"
          value="Populated"
          style="width: 200px"
        ></op-input>

        <op-input
          label="Label*"
          placeholder="Populated"
          name="mat-input-2"
          value="Populated"
          required
          style="width: 200px"
        ></op-input>

        <op-input
          label="Label"
          placeholder="Populated"
          name="mat-input-3"
          value="Populated"
          style="width: 200px"
          error
        ></op-input>

        <op-input
          label="Label*"
          placeholder="Populated"
          name="mat-input-4"
          value="Populated"
          required
          style="width: 200px"
          error
        ></op-input>

        <op-input
          label="Label"
          placeholder="Populated"
          name="mat-input-5"
          value="Populated"
          style="width: 200px"
          disabled
        ></op-input>

        <op-input
          label="Label*"
          placeholder="Populated"
          name="mat-input-6"
          value="Populated"
          required
          style="width: 200px"
          disabled
        ></op-input>
      </div>

      <h3>Input Field Examples</h3>
      <div style="max-width: 400px">
        <op-input
          label="Username"
          placeholder="Enter your username"
          name="username"
          required
          helperText="Your username should be at least 3 characters long"
        ></op-input>

        <op-input
          label="Email"
          placeholder="Enter your email"
          name="email"
          type="email"
          required
        ></op-input>

        <op-input
          label="Password"
          placeholder="Enter your password"
          name="password"
          type="password"
          required
        ></op-input>

        <op-input
          label="Error Example"
          placeholder="This field has an error"
          name="error-example"
          error
          errorMessage="This field is required"
        ></op-input>

        <op-input
          label="Disabled Example"
          placeholder="This field is disabled"
          name="disabled-example"
          disabled
        ></op-input>

        <h4>Search Input</h4>
        <op-input label="Search" placeholder="Search..." name="search">
          <div slot="prefix">
            <svg
              width="24"
              height="24"
              viewBox="0 0 24 24"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M15.5 14H14.71L14.43 13.73C15.41 12.59 16 11.11 16 9.5C16 5.91 13.09 3 9.5 3C5.91 3 3 5.91 3 9.5C3 13.09 5.91 16 9.5 16C11.11 16 12.59 15.41 13.73 14.43L14 14.71V15.5L19 20.49L20.49 19L15.5 14ZM9.5 14C7.01 14 5 11.99 5 9.5C5 7.01 7.01 5 9.5 5C11.99 5 14 7.01 14 9.5C14 11.99 11.99 14 9.5 14Z"
                fill="currentColor"
              />
            </svg>
          </div>
        </op-input>

        <h4>Input with Custom Prefix and Suffix</h4>
        <op-input
          label="Custom Slots"
          placeholder="Enter text..."
          name="custom-slots"
        >
          <div slot="prefix" style="color: blue">@</div>
          <div slot="suffix" style="color: green">✓</div>
        </op-input>
      </div>

      <h3>Value Proposition</h3>
      <div style="margin-bottom: 50px"></div>
      <div style="display: flex; gap: 24px; margin-bottom: 50px">
        <op-value-proposition
          title="Default Shield"
          description="This is the default shield icon without any custom icon inside."
          variant="white"
        ></op-value-proposition>

        <op-value-proposition
          title="Custom Icon"
          description="This example uses a custom icon inside the shield."
          variant="white"
        >
          <svg
            slot="icon"
            xmlns="http://www.w3.org/2000/svg"
            width="24"
            height="24"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            stroke-width="2"
            stroke-linecap="round"
            stroke-linejoin="round"
          >
            <path d="M12 2L2 7l10 5 10-5-10-5z"></path>
            <path d="M2 17l10 5 10-5"></path>
            <path d="M2 12l10 5 10-5"></path>
          </svg>
        </op-value-proposition>

        <op-value-proposition
          title="Blue Variant"
          description="This is an example with a blue background and a custom icon."
          variant="blue"
        >
          <svg
            slot="icon"
            xmlns="http://www.w3.org/2000/svg"
            width="24"
            height="24"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            stroke-width="2"
            stroke-linecap="round"
            stroke-linejoin="round"
          >
            <circle cx="12" cy="12" r="10"></circle>
            <line x1="12" y1="8" x2="12" y2="12"></line>
            <line x1="12" y1="16" x2="12.01" y2="16"></line>
          </svg>
        </op-value-proposition>
      </div>

      <h3>Card</h3>
      <div style="margin-bottom: 20px"></div>
      <div
        style="
          display: flex;
          flex-direction: column;
          gap: 24px;
          margin-bottom: 50px;
          max-width: 500px;
        "
      >
        <!-- Card with icon -->
        <op-card title="SBG Streaming" description="Streaming Service" elevated>
          <div
            slot="icon"
            style="
              width: 70px;
              height: 70px;
              border-radius: 50%;
              background-color: #0033aa;
              display: flex;
              align-items: center;
              justify-content: center;
              overflow: hidden;
            "
          >
            <svg
              width="50"
              height="50"
              viewBox="0 0 129 86"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
              style="margin-top: 10px"
            >
              <path
                d="M98.0376 7.44807C97.8984 5.47056 96.6078 3.73894 94.6569 3.3038C84.6046 1.06103 74.3113 0 64 0C53.6887 0 43.3954 1.06103 33.3431 3.3038C31.3937 3.73894 30.1016 5.47056 29.9624 7.44807C29.3665 15.9393 27.3123 37.665 28.2346 46.417C30.3561 66.1325 44.4644 78.7367 61.4023 85.17C62.2378 85.4874 63.0687 85.769 63.9476 86C63.9626 85.9955 63.985 85.9881 64 85.9836C64.015 85.9881 64.0389 85.9955 64.0524 86C64.9313 85.769 65.7637 85.4933 66.5977 85.17C83.5805 78.853 97.6439 66.134 99.7654 46.417C100.688 37.665 98.6335 15.9393 98.0376 7.44807Z"
                fill="white"
              />
            </svg>
          </div>

          <p>
            SBG Streaming is a streaming service that offers a wide variety of
            award-winning TV shows, movies, documentaries, and more on thousands
            of internet-connected devices.
          </p>
          <p>Watch anywhere. Cancel anytime.</p>
          <div slot="footer">
            <op-button>ACTION</op-button>
            <op-button kind="secondary">ACTION</op-button>
          </div>
        </op-card>

        <!-- Card without icon -->
        <op-card title="Card without icon" description="Subtitle text">
          <p>
            This card doesn't have an icon, so the header content shifts to the
            left.
          </p>
          <div slot="footer">
            <op-button>ACTION</op-button>
          </div>
        </op-card>
      </div>

      <h3>Table</h3>
      <div style="margin-bottom: 20px"></div>
      <div style="margin-bottom: 50px">
        <!-- Basic Table -->
        <h4>Basic Table</h4>
        <op-table id="basic-table" style="margin-bottom: 24px;">
          <op-table-column key="name" label="Name" sortable></op-table-column>
          <op-table-column key="email" label="Email" sortable></op-table-column>
          <op-table-column key="role" label="Role"></op-table-column>
          <op-table-column key="status" label="Status" align="center"></op-table-column>
        </op-table>

        <!-- Striped Table -->
        <h4>Striped & Bordered Table</h4>
        <op-table id="striped-table" striped bordered style="margin-bottom: 24px;">
          <op-table-column key="product" label="Product" sortable></op-table-column>
          <op-table-column key="price" label="Price" align="right" sortable columnType="number"></op-table-column>
          <op-table-column key="category" label="Category"></op-table-column>
          <op-table-column key="stock" label="Stock" align="center" sortable columnType="number"></op-table-column>
        </op-table>

        <!-- Dense Table -->
        <h4>Dense Table</h4>
        <op-table id="dense-table" dense bordered style="margin-bottom: 24px;">
          <op-table-column key="id" label="ID" width="80px" align="center"></op-table-column>
          <op-table-column key="task" label="Task" sortable></op-table-column>
          <op-table-column key="priority" label="Priority" align="center"></op-table-column>
          <op-table-column key="assignee" label="Assignee"></op-table-column>
        </op-table>

        <!-- Empty Table -->
        <h4>Empty Table</h4>
        <op-table id="empty-table" emptyMessage="No records found">
          <op-table-column key="name" label="Name"></op-table-column>
          <op-table-column key="value" label="Value"></op-table-column>
        </op-table>
      </div>

      <h3>Detailed Product Card</h3>
      <div style="margin-bottom: 20px"></div>
      <div style="margin-bottom: 50px">
        <op-detailed-product-card
          title="Global Trading Platform"
          description="Access international markets with our advanced trading technology and real-time analytics."
        >
          <div
            slot="image"
            style="
              width: 100%;
              height: 100%;
              background: linear-gradient(45deg, #1e40af, #3b82f6);
              display: flex;
              align-items: center;
              justify-content: center;
              color: white;
              font-size: 18px;
              font-weight: bold;
              text-align: center;
            "
          >
            Product<br />Image
          </div>
          <op-button slot="actions">START TRADING</op-button>
        </op-detailed-product-card>
      </div>
    </div>

    <script>
      const opTabs = document.querySelector('op-tab-list')
      opTabs.addEventListener('updateValue', (e) => {
        console.log(e.detail)
      })

      const inputs = document.querySelectorAll('op-input')
      inputs.forEach((input) => {
        input.addEventListener('updateValue', (e) => {
          console.log(`Input ${input.name} value:`, e.detail.value)
        })
      })

      // Table data
      const userData = [
        { name: 'John Doe', email: '<EMAIL>', role: 'Admin', status: 'Active' },
        { name: 'Jane Smith', email: '<EMAIL>', role: 'User', status: 'Active' },
        { name: 'Bob Johnson', email: '<EMAIL>', role: 'Editor', status: 'Inactive' },
        { name: 'Alice Brown', email: '<EMAIL>', role: 'User', status: 'Active' },
        { name: 'Charlie Wilson', email: '<EMAIL>', role: 'Admin', status: 'Pending' }
      ]

      const productData = [
        { product: 'Laptop Pro', price: '$1,299', category: 'Electronics', stock: 15 },
        { product: 'Wireless Mouse', price: '$29', category: 'Accessories', stock: 50 },
        { product: 'Monitor 4K', price: '$399', category: 'Electronics', stock: 8 },
        { product: 'Keyboard Mechanical', price: '$89', category: 'Accessories', stock: 25 },
        { product: 'Webcam HD', price: '$79', category: 'Electronics', stock: 12 }
      ]

      const taskData = [
        { id: 1, task: 'Update documentation', priority: 'High', assignee: 'John' },
        { id: 2, task: 'Fix login bug', priority: 'Critical', assignee: 'Jane' },
        { id: 3, task: 'Design new feature', priority: 'Medium', assignee: 'Bob' },
        { id: 4, task: 'Code review', priority: 'Low', assignee: 'Alice' },
        { id: 5, task: 'Deploy to staging', priority: 'High', assignee: 'Charlie' }
      ]

      // Set table data
      document.getElementById('basic-table').data = userData
      document.getElementById('striped-table').data = productData
      document.getElementById('dense-table').data = taskData
      document.getElementById('empty-table').data = []

      // Add table event listeners
      const tables = document.querySelectorAll('op-table')
      tables.forEach((table) => {
        table.addEventListener('updateSort', (e) => {
          console.log(`Table sort:`, e.detail)
        })
      })
    </script>
  </body>
</html>
