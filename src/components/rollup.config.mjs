// cSpell:ignore onwarn
import { defineConfig } from 'rollup'
import terser from '@rollup/plugin-terser'
import resolve from '@rollup/plugin-node-resolve'
import replace from '@rollup/plugin-replace'
import summary from 'rollup-plugin-summary'
import { properties, events } from './dist/replaceable.mjs'

const replaceValuesForLwc = (input) =>
  Object.values(input).reduce((acc, prop) => {
    acc[`"${prop}"`] = `"${prop.toLowerCase()}"`
    return acc
  }, {})

export default defineConfig({
  input: 'dist/index.mjs',
  output: {
    file: 'bundle.js',
    format: 'esm',
  },
  onwarn(warning) {
    if (warning.code !== 'THIS_IS_UNDEFINED') {
      console.error(`(!) ${warning.message}`)
    }
  },
  plugins: [
    replace({
      preventAssignment: false,
      delimiters: ['', ''],
      values: {
        ...replaceValuesForLwc(properties),
        ...replaceValuesForLwc(events),
        'Reflect.decorate': 'undefined',
      },
    }),
    resolve(),
    terser({
      ecma: 2021,
      module: true,
      warnings: true,
    }),
    summary(),
  ],
})
