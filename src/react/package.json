{"name": "react", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"onepulse-components": "file:../components", "react": "^19.0.0", "react-dom": "^19.0.0"}, "devDependencies": {"@eslint/js": "^9.17.0", "@types/react": "^19.0.0", "@types/react-dom": "^19.0.0", "@vitejs/plugin-react-swc": "^3.5.0", "eslint": "^9.17.0", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.16", "globals": "^15.14.0", "typescript": "~5.6.2", "typescript-eslint": "^8.18.2", "vite": "^6.0.5"}}