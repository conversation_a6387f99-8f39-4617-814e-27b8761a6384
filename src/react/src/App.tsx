import { useState } from 'react'
import reactLogo from './assets/react.svg'
import viteLogo from '/vite.svg'
import './App.css'
import { OpButton } from 'onepulse-components/react/button'
import { OpTabList, OpTab } from 'onepulse-components/react/tabs'
import { OpTabListUpdateValueEvent } from 'onepulse-components/tabs'

function App() {
  const [count, setCount] = useState(0)

  const [activeTab, setActiveTab] = useState('3')
  const [isTabListSpacious, setIsTabListSpacious] = useState(false)
  const onTabListValueChange = (e: OpTabListUpdateValueEvent) => {
    const customEvent = e
    console.log('Tab change', customEvent.detail)
    setActiveTab(customEvent.detail.value)
    setIsTabListSpacious(!isTabListSpacious)
  }

  return (
    <>
      <div>
        <a href="https://vite.dev" target="_blank">
          <img src={viteLogo} className="logo" alt="Vite logo" />
        </a>
        <a href="https://react.dev" target="_blank">
          <img src={reactLogo} className="logo react" alt="React logo" />
        </a>
      </div>
      <h1>Vite + React</h1>
      <div className="card">
        <button onClick={() => setCount((count) => count + 1)}>
          count is {count}
        </button>
        <p>
          Edit <code>src/App.tsx</code> and save to test HMR
        </p>
      </div>
      <p className="read-the-docs">
        Click on the Vite and React logos to learn more
      </p>
      <OpButton
        kind="secondary"
        dense={false}
        onClick={() => setCount((count) => count - 1)}
      >
        Click me {count}
      </OpButton>
      <OpTabList
        value={activeTab}
        spaciousHeight={isTabListSpacious}
        onupdateValue={onTabListValueChange}
      >
        <OpTab value="1">Home</OpTab>
        <OpTab value="2">Profile</OpTab>
        <OpTab value="3">Settings</OpTab>
      </OpTabList>
    </>
  )
}

export default App
