import { Component, CUSTOM_ELEMENTS_SCHEMA } from '@angular/core'
import { RouterOutlet } from '@angular/router'

import 'onepulse-components/button'
import 'onepulse-components/tabs'

@Component({
  selector: 'app-root',
  imports: [RouterOutlet],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  templateUrl: './app.component.html',
  styleUrl: './app.component.scss',
})
export class AppComponent {
  title = 'angular'

  isButtonCritical = false

  activeTab = '3'

  onButtonClick() {
    console.log('Button clicked')
    this.isButtonCritical = !this.isButtonCritical
  }

  onTabChange(event: Event) {
    const customEvent = event as CustomEvent
    console.log('Tab change', customEvent.detail)
    this.activeTab = customEvent.detail.value
  }
}
