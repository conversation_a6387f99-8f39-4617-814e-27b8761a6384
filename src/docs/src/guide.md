---
outline: deep
---

# Guide

This guide will help you get started with OnePulse Components.

## Installation

The components are distributed as archives that can be downloaded from the [releases page](https://github.com/standardbank-cibbt/onepulse-components/releases). There are two types of archives available:

- **NPM Package**: This archive contains the compiled components and their dependencies. It can be installed using a package manager like `npm` or `yarn`.
- **JavaScript Bundle**: This archive contains a single JavaScript file that can be included in a web page using a `<script>` tag.

### NPM Package

If your project uses a module bundler like Webpack or Vite, you can install the components using `npm` or `yarn`.

- Download the NPM package archive from the releases page.
- Extract the archive to a directory in your project.
- Install the package using `npm` or `yarn`.

```bash
npm install /path/to/onepulse-components-1.0.0.tgz
```

### JavaScript Bundle

If your project does not use a module bundler, you can include the JavaScript bundle in your HTML file.

- Download the JavaScript bundle archive from the releases page.
- Extract the archive to a directory in your project.
- Include the JavaScript bundle in your HTML file.

```html
<script src="/path/to/onepulse-components-1.0.0.js"></script>
```

## Usage

### Vue

#### Vite configuration

Extend the native elements list in the `vite.config.js` file to include the custom elements from OnePulse Components.

```js
import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'

export default defineConfig({
  plugins: [
    vue({
      template: {
        compilerOptions: {
          isCustomElement: (tag) => tag.startsWith('op-'),
        },
      },
    }),
  ],
})
```

#### Usage

Import the components in your Vue file and use them in the template.

```vue [Vue]
<script setup lang="ts">
import 'onepulse-components/button'
</script>
<template>
  <main>
    <op-button>Click me</op-button>
  </main>
</template>
```

#### Using components with named slots

Some components have named slots that can be used to customize the content, such as the [`op-dialog` component](/components/dialog). Please disable the [vue/no-deprecated-slot-attribute](https://eslint.vuejs.org/rules/no-deprecated-slot-attribute.html) eslint rule if you run into issues with the `slot` attribute.

### Angular

#### Usage

Add the `CUSTOM_ELEMENTS_SCHEMA` to the `schemas` array in the Angular component that uses the custom elements from OnePulse Components.

```ts [Angular]
import { Component, CUSTOM_ELEMENTS_SCHEMA } from '@angular/core'
import 'onepulse-components/button'

@Component({
  selector: 'app-root',
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  template: `
    <main>
      <op-button>Click me</op-button>
    </main>
  `,
})
export class AppComponent {}
```

### React

#### React package exports

We added wrappers for the custom elements to make them easier to use in React. You can import the components from the `onepulse-components/react` entry point. More information can be found on on the [Lit documentation](https://lit.dev/docs/frameworks/react/) page on using Lit with React.

```tsx [React]
import { OpButton } from 'onepulse-components/react/button'

function App() {
  return (
    <main>
      <OpButton>Click me</OpButton>
    </main>
  )
}
```

#### Using components with named slots

Some components have named slots that can be used to customize the content, such as the [`op-dialog` component](/components/dialog). If you want to render a React component in a named slot, wrap the component in container element since React components are not themselves Html elements. Use `display: contents;` to prevent the container element from affecting the layout.

```tsx [React]
import { OpDialog } from 'onepulse-components/react/dialog'

function App() {
  return (
    <main>
      <OpDialog>
        <div slot="header" style="display: contents;">
          <ReactHeaderComponent />
        </div>
        <div slot="content" style="display: contents;">
          <ReactContentComponent />
        </div>
        <div slot="footer" style="display: contents;">
          <ReactFooterComponent />
        </div>
      </OpDialog>
    </main>
  )
}
```

#### Using components with event listeners

The components wrapped for React have slightly different event names. For example, the `updateValue` event in the `op-tab-list` component is renamed to `onupdateValue` in the React wrapper. They usually follow the pattern `on` + `event name`. Please refer to the component documentation for the correct event names.

### No framework

#### Usage

Include the JavaScript bundle in your HTML file and use the custom elements in your markup. The one file contains all the components and their dependencies. We don't have a separate bundle for each component.

### Salesforce LWC

TODO
