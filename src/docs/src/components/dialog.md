---
outline: deep
---

<script setup lang="ts">
import 'onepulse-components/button'
import 'onepulse-components/dialog'
import { ref } from 'vue'
import { type OpDialogUpdateVisibleEvent } from 'onepulse-components/dialog'

const isDialogVisible = ref(false)
const onOpenDialog = () => {
  isDialogVisible.value = true
}

const onUpdatedVisible = (event: OpDialogUpdateVisibleEvent) => {
  isDialogVisible.value = event.detail.visible
}

const onConfirm = () => {
  // Do something
  isDialogVisible.value = false
}

const onCancel = () => {
  // Do something
  isDialogVisible.value = false
}
</script>

# Dialog

The `op-dialog` component is a modal dialog that can be used to display content or request user input. The dialog can be opened and closed programmatically or by user interaction.

## Api

### Props

| Name      | Type        | Default | Description                                                                                                                                                                           |
| --------- | ----------- | ------- | ------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| `visible` | `boolean`   | `false` | Whether the dialog is visible. When set to `true`, the dialog is displayed.                                                                                                           |
| `classes` | `ClassInfo` | `{}`    | Additional classes to add to the dialog. For example, `{foo: bar}` applies the class `foo` if the value of `bar` is truthy.                                                           |
| `styles`  | `StyleInfo` | `{}`    | Additional styles to add to the dialog. For example, `{backgroundColor: 'red', 'border-top': '5px', '--size': '0'}` sets the `background-color`, `border-top` and `--size` properties |

### Events

| Name            | React Name        | Type                         | Description                                                                                                                                                                                                                                                                              |
| --------------- | ----------------- | ---------------------------- | ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| `updateVisible` | `onupdateVisible` | `OpDialogUpdateVisibleEvent` | Emitted when the dialog visibility changes. The new visibility state is passed as the event payload. This is a `CustomEvent<{ visible: boolean }>`. The event is only emitted when dialog is closed by user interaction, such as clicking the close icon, instead of direct state change |

### Slots

| Name      | Description                                                                      |
| --------- | -------------------------------------------------------------------------------- |
| `header`  | The header content of the dialog. This is displayed at the top of the dialog.    |
| `content` | The main content of the dialog. This is displayed in the middle of the dialog.   |
| `footer`  | The footer content of the dialog. This is displayed at the bottom of the dialog. |

## Usage

::: code-group

```vue [Vue]
<!-- eslint-disable vue/no-deprecated-slot-attribute -->
<script setup lang="ts">
import 'onepulse-components/button'
import 'onepulse-components/dialog'
import { ref } from 'vue'
import { type OpDialogUpdateVisibleEvent } from 'onepulse-components/dialog'

const isDialogVisible = ref(false)
const onOpenDialog = () => {
  isDialogVisible.value = true
}

const onUpdatedVisible = (event: OpDialogUpdateVisibleEvent) => {
  isDialogVisible.value = event.detail.visible
}

const onConfirm = () => {
  // Do something
  isDialogVisible.value = false
}

const onCancel = () => {
  // Do something
  isDialogVisible.value = false
}
</script>

<template>
  <main>
    <op-button @click="onOpenDialog">Open Dialog</op-button>

    <op-dialog
      :styles="{ width: '560px' }"
      :visible="isDialogVisible"
      @updateVisible="onUpdatedVisible"
    >
      <span slot="header">Unsubscribe to API</span>
      <p slot="content" style="text-align: center">
        Are you sure you want to unsubscribe from the Authentifi ? Once
        confirmed, you will no longer have access, and any integrations relying
        on this API may be affected.
      </p>
      <div slot="footer" style="display: flex; justify-content: space-between">
        <op-button kind="tertiary" @click="onCancel">Cancel</op-button>
        <op-button @click="onConfirm"> Confirm</op-button>
      </div>
    </op-dialog>
  </main>
</template>
```

```ts [Angular]
import { CommonModule } from '@angular/common'
import { Component, CUSTOM_ELEMENTS_SCHEMA } from '@angular/core'
import 'onepulse-components/button'
import 'onepulse-components/dialog'
import { OpDialogUpdateVisibleEvent } from 'onepulse-components/dialog'

@Component({
  selector: 'app-root',
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  imports: [CommonModule],
  template: `
    <main>
      <op-button (click)="onOpenDialog()">Open Dialog</op-button>

      <op-dialog
        [styles]="{ width: '560px' }"
        [visible]="isDialogVisible"
        (updateVisible)="onUpdatedVisible($event)"
      >
        <span slot="header">Unsubscribe to API</span>
        <p slot="content" style="text-align: center">
          Are you sure you want to unsubscribe from the Authentifi ? Once
          confirmed, you will no longer have access, and any integrations
          relying on this API may be affected.
        </p>
        <div
          slot="footer"
          style="display: flex; justify-content: space-between"
        >
          <op-button kind="tertiary" (click)="onCancel()">Cancel</op-button>
          <op-button (click)="onConfirm()"> Confirm</op-button>
        </div>
      </op-dialog>
    </main>
  `,
  styleUrls: [],
})
export class AppComponent {
  isDialogVisible = false

  onOpenDialog() {
    this.isDialogVisible = true
  }

  onUpdatedVisible(event: Event) {
    const customEvent = event as OpDialogUpdateVisibleEvent
    this.isDialogVisible = customEvent.detail.visible
  }

  onConfirm() {
    // Do something
    this.isDialogVisible = false
  }

  onCancel() {
    // Do something
    this.isDialogVisible = false
  }
}
```

```tsx [React]
import { useState } from 'react'
import { OpButton } from 'onepulse-components/react/button'
import { OpDialog } from 'onepulse-components/react/dialog'
import { OpDialogUpdateVisibleEvent } from 'onepulse-components/dialog'

function App() {
  const [isDialogVisible, setIsDialogVisible] = useState(false)

  const onOpenDialog = () => {
    setIsDialogVisible(true)
  }

  const onUpdatedVisible = (event: OpDialogUpdateVisibleEvent) => {
    setIsDialogVisible(event.detail.visible)
  }

  const onConfirm = () => {
    // Do something
    setIsDialogVisible(false)
  }

  const onCancel = () => {
    // Do something
    setIsDialogVisible(false)
  }

  return (
    <main>
      <OpButton onClick={onOpenDialog}>Open Dialog</OpButton>

      <OpDialog
        styles={{ width: '560px' }}
        visible={isDialogVisible}
        onupdateVisible={onUpdatedVisible}
      >
        <span slot="header">Unsubscribe to API</span>
        <p slot="content" style={{ textAlign: 'center' }}>
          Are you sure you want to unsubscribe from the Authentifi? Once
          confirmed, you will no longer have access, and any integrations
          relying on this API may be affected.
        </p>
        <div
          slot="footer"
          style={{ display: 'flex', justifyContent: 'space-between' }}
        >
          <OpButton kind="tertiary" onClick={onCancel}>
            Cancel
          </OpButton>
          <OpButton onClick={onConfirm}>Confirm</OpButton>
        </div>
      </OpDialog>
    </main>
  )
}

export default App
```

```html [No framework]
<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Document</title>
    <script type="module" src="/src/index.mts"></script>
    <style>
      .dialog-width {
        width: 300px;
      }
    </style>
  </head>

  <op-button id="open">Open Dialog</op-button>

  <op-dialog id="dialog"  styles='{"width":"560px"}' >
    <span slot="header">Unsubscribe to API</span>
    <p slot="content" style="text-align: center;">Are you sure you want to unsubscribe from the Authentifi ? Once confirmed, you will no longer have access, and any integrations relying on this API may be affected. </p>
    <div slot="footer" style="display: flex; justify-content: space-between;">
      <op-button id="cancel-button" kind="tertiary">Cancel</op-button>
      <op-button id="confirm-button"> Confirm</op-button>
    </div>
  </op-dialog>

  <script>
    document.addEventListener('DOMContentLoaded', function() {

      const openButton = document.getElementById('open');
      const dialog = document.getElementById('dialog');
      openButton.addEventListener('click', function() {
        dialog.visible = true
      });
      dialog.addEventListener('updateVisible', function(e) {
        console.log(e.detail.visible);

      });

      const cancelButton = document.getElementById('cancel-button');
      cancelButton.addEventListener('click', function() {
        // Do something
        dialog.visible = false;
      });

      const confirmButton = document.getElementById('confirm-button');
      confirmButton.addEventListener('click', function() {
        // Do something
        dialog.visible = false;
      });

    });
  </script>
</body>
</html>

```

```html [LWC html]
<template>
  <template lwc:if="{componentsLoaded}">
    <op-button lwc:external onclick="{onOpenDialog}">Open Dialog</op-button>
    <op-dialog lwc:external styles="{dialogStyles}" visible="{isDialogVisible}">
      <span slot="header">Unsubscribe to API</span>
      <p slot="content" style="text-align: center">
        Are you sure you want to unsubscribe from the Authentifi ? Once
        confirmed, you will no longer have access, and any integrations relying
        on this API may be affected.
      </p>
      <div slot="footer" style="display: flex; justify-content: space-between">
        <op-button lwc:external kind="tertiary" onclick="{onCancel}"
          >Cancel</op-button
        >
        <op-button lwc:external onclick="{onConfirm}"> Confirm</op-button>
      </div>
    </op-dialog>
  </template>
</template>
```

```js [LWC js]
import { LightningElement } from 'lwc'
import OSB_Components from '@salesforce/resourceUrl/OSB_Components'
import { loadScript } from 'lightning/platformResourceLoader'

export default class App extends LightningElement {
  isDialogVisible = false
  componentsLoaded = false
  dialogStyles = { width: '560px' }

  async renderedCallback() {
    await loadScript(this, OSB_Components)
    setTimeout(() => {
      this.componentsLoaded = true
      this.template
        .querySelector('op-dialog')
        .addEventListener('updateVisible', this.onUpdatedVisible.bind(this))
    }, 10000)
  }

  onOpenDialog() {
    this.isDialogVisible = true
  }

  onUpdatedVisible(event) {
    this.isDialogVisible = event.detail.visible
  }

  onConfirm() {
    // Do something
    this.isDialogVisible = false
  }

  onCancel() {
    // Do something
    this.isDialogVisible = false
  }
}
```

:::

## Examples

<op-button @click="onOpenDialog">Open Dialog</op-button>
<op-dialog
:styles="{ width: '560px' }"
:visible="isDialogVisible"
@updateVisible="onUpdatedVisible" >
<span slot="header">Unsubscribe to API</span>

<p slot="content" style="text-align: center">
Are you sure you want to unsubscribe from the Authentifi ? Once
confirmed, you will no longer have access, and any integrations relying
on this API may be affected.
</p>
<div slot="footer" style="display: flex; justify-content: space-between">
<op-button kind="tertiary" @click="onCancel">Cancel</op-button>
<op-button @click="onConfirm"> Confirm</op-button>
</div>
</op-dialog>
