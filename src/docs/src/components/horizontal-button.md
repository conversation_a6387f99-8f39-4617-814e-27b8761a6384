---
outline: deep
---

<script setup lang="ts">
import 'onepulse-components/horizontal-button'
import { ref } from 'vue'

const title = ref('eMarket trader')
const description = ref('Requires OneHub authenticator app')
const closable = ref(true)
const styles = { width: '200px' }
const show = ref(true)

const onClick = () => {
  closable.value = !closable.value
}

const onClose = () => {
  show.value = false
}
</script>

# Horizontal button

The `op-horizontal-button` component.

## Api

### Props

| Name          | Type        | Default | Description                                                                                                                                                                           |
| ------------- | ----------- | ------- | ------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| `title`       | `string`    | `''`    | The title text displayed in the horizontal button                                                                                                                                     |
| `description` | `string`    | `''`    | The description text displayed in the horizontal button                                                                                                                               |
| `closable`    | `boolean`   | `false` | Whether the horizontal button is closable                                                                                                                                             |
| `classes`     | `ClassInfo` | `{}`    | Additional classes to add to the dialog. For example, `{foo: bar}` applies the class `foo` if the value of `bar` is truthy.                                                           |
| `styles`      | `StyleInfo` | `{}`    | Additional styles to add to the dialog. For example, `{backgroundColor: 'red', 'border-top': '5px', '--size': '0'}` sets the `background-color`, `border-top` and `--size` properties |

### Events

The native `click` event is emitted when the horizontal button is clicked.

| Name    | React Name | Type    | Description                                                                           |
| ------- | ---------- | ------- | ------------------------------------------------------------------------------------- |
| `close` | `onclose`  | `Event` | Emitted when the close icon is clicked when the `closable` property is set to `true`. |

### Slots

| Name   | Description                                               |
| ------ | --------------------------------------------------------- |
| `icon` | Custom icon to be displayed inside the horizontal button. |

## Usage

::: code-group

```vue [Vue]
<script setup lang="ts">
import 'onepulse-components/horizontal-button'
import { ref } from 'vue'

const title = ref('eMarket trader')
const description = ref('Requires OneHub authenticator app')
const closable = ref(true)
const styles = { width: '200px' }
const show = ref(true)

const onClick = () => {
  closable.value = !closable.value
}

const onClose = () => {
  show.value = false
}
</script>

<template>
  <main>
    <op-horizontal-button
      :title="title"
      :description="description"
      :closable="closable"
      :styles="styles"
      @click="onClick"
      @close="onClose"
      v-if="show"
    >
      <div
        slot="icon"
        style="
          width: 70px;
          height: 70px;
          border-radius: 50%;
          background-color: #0033aa;
          display: flex;
          align-items: center;
          justify-content: center;
          overflow: hidden;
        "
      >
        <svg
          width="50"
          height="50"
          viewBox="0 0 129 86"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
          style="margin-top: 10px"
        >
          <path
            d="M98.0376 7.44807C97.8984 5.47056 96.6078 3.73894 94.6569 3.3038C84.6046 1.06103 74.3113 0 64 0C53.6887 0 43.3954 1.06103 33.3431 3.3038C31.3937 3.73894 30.1016 5.47056 29.9624 7.44807C29.3665 15.9393 27.3123 37.665 28.2346 46.417C30.3561 66.1325 44.4644 78.7367 61.4023 85.17C62.2378 85.4874 63.0687 85.769 63.9476 86C63.9626 85.9955 63.985 85.9881 64 85.9836C64.015 85.9881 64.0389 85.9955 64.0524 86C64.9313 85.769 65.7637 85.4933 66.5977 85.17C83.5805 78.853 97.6439 66.134 99.7654 46.417C100.688 37.665 98.6335 15.9393 98.0376 7.44807Z"
            fill="white"
          />
        </svg>
      </div>
    </op-horizontal-button>
  </main>
</template>
```

```ts [Angular]
import { Component, CUSTOM_ELEMENTS_SCHEMA } from '@angular/core'
import 'onepulse-components/horizontal-button'

@Component({
  selector: 'app-root',
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  template: `
    <main>
      @if (show) {
        <op-horizontal-button
          [title]="title"
          [description]="description"
          [closable]="closable"
          [styles]="styles"
          (click)="onClick()"
          (close)="onClose()"
        >
          <div
            slot="icon"
            style="
          width: 70px;
          height: 70px;
          border-radius: 50%;
          background-color: #0033aa;
          display: flex;
          align-items: center;
          justify-content: center;
          overflow: hidden;
        "
          >
            <svg
              width="50"
              height="50"
              viewBox="0 0 129 86"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
              style="margin-top: 10px"
            >
              <path
                d="M98.0376 7.44807C97.8984 5.47056 96.6078 3.73894 94.6569 3.3038C84.6046 1.06103 74.3113 0 64 0C53.6887 0 43.3954 1.06103 33.3431 3.3038C31.3937 3.73894 30.1016 5.47056 29.9624 7.44807C29.3665 15.9393 27.3123 37.665 28.2346 46.417C30.3561 66.1325 44.4644 78.7367 61.4023 85.17C62.2378 85.4874 63.0687 85.769 63.9476 86C63.9626 85.9955 63.985 85.9881 64 85.9836C64.015 85.9881 64.0389 85.9955 64.0524 86C64.9313 85.769 65.7637 85.4933 66.5977 85.17C83.5805 78.853 97.6439 66.134 99.7654 46.417C100.688 37.665 98.6335 15.9393 98.0376 7.44807Z"
                fill="white"
              />
            </svg>
          </div>
        </op-horizontal-button>
      }
    </main>
  `,
  styles: [],
})
export class AppComponent {
  title = 'eMarket trader'
  description = 'Requires OneHub authenticator app'
  closable = true
  styles = { width: '200px' }
  show = true

  onClick = () => {
    this.closable = !this.closable
  }

  onClose = () => {
    this.show = false
  }
}
```

```tsx [React]
import { OpHorizontalButton } from 'onepulse-components/react/horizontal-button'
import { useState } from 'react'

const App = () => {
  const [title] = useState('eMarket trader')
  const [description] = useState('Requires OneHub authenticator app')
  const [closable, setClosable] = useState(true)
  const [show, setShow] = useState(true)

  const styles = { width: '200px' }

  const handleClick = () => {
    setClosable(!closable)
  }

  const handleClose = () => {
    setShow(false)
  }

  return (
    <main>
      {show && (
        <OpHorizontalButton
          title={title}
          description={description}
          closable={closable}
          styles={styles}
          onClick={handleClick}
          onclose={handleClose}
        >
          <div
            slot="icon"
            style={{
              width: '70px',
              height: '70px',
              borderRadius: '50%',
              backgroundColor: '#0033aa',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              overflow: 'hidden',
            }}
          >
            <svg
              width="50"
              height="50"
              viewBox="0 0 129 86"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
              style={{ marginTop: '10px' }}
            >
              <path
                d="M98.0376 7.44807C97.8984 5.47056 96.6078 3.73894 94.6569 3.3038C84.6046 1.06103 74.3113 0 64 0C53.6887 0 43.3954 1.06103 33.3431 3.3038C31.3937 3.73894 30.1016 5.47056 29.9624 7.44807C29.3665 15.9393 27.3123 37.665 28.2346 46.417C30.3561 66.1325 44.4644 78.7367 61.4023 85.17C62.2378 85.4874 63.0687 85.769 63.9476 86C63.9626 85.9955 63.985 85.9881 64 85.9836C64.015 85.9881 64.0389 85.9955 64.0524 86C64.9313 85.769 65.7637 85.4933 66.5977 85.17C83.5805 78.853 97.6439 66.134 99.7654 46.417C100.688 37.665 98.6335 15.9393 98.0376 7.44807Z"
                fill="white"
              />
            </svg>
          </div>
        </OpHorizontalButton>
      )}
    </main>
  )
}

export default App
```

```html [No framework]
<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Document</title>
    <script type="module" src="/src/index.mts"></script>
  </head>
  <body>
    <main>
      <op-horizontal-button
        title="eMarket trader"
        description="Requires OneHub authenticator app"
        closable
        styles='{"width":"200px"}'
      >
        <div
          slot="icon"
          style="
            width: 70px;
            height: 70px;
            border-radius: 50%;
            background-color: #0033aa;
            display: flex;
            align-items: center;
            justify-content: center;
            overflow: hidden;
          "
        >
          <svg
            width="50"
            height="50"
            viewBox="0 0 129 86"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            style="margin-top: 10px"
          >
            <path
              d="M98.0376 7.44807C97.8984 5.47056 96.6078 3.73894 94.6569 3.3038C84.6046 1.06103 74.3113 0 64 0C53.6887 0 43.3954 1.06103 33.3431 3.3038C31.3937 3.73894 30.1016 5.47056 29.9624 7.44807C29.3665 15.9393 27.3123 37.665 28.2346 46.417C30.3561 66.1325 44.4644 78.7367 61.4023 85.17C62.2378 85.4874 63.0687 85.769 63.9476 86C63.9626 85.9955 63.985 85.9881 64 85.9836C64.015 85.9881 64.0389 85.9955 64.0524 86C64.9313 85.769 65.7637 85.4933 66.5977 85.17C83.5805 78.853 97.6439 66.134 99.7654 46.417C100.688 37.665 98.6335 15.9393 98.0376 7.44807Z"
              fill="white"
            />
          </svg>
        </div>
      </op-horizontal-button>
    </main>

    <script>
      const horizontalButton = document.querySelector('op-horizontal-button')
      horizontalButton.addEventListener('close', () => {
        horizontalButton.style.display = 'none'
      })
      horizontalButton.addEventListener('click', () => {
        horizontalButton.closable = !horizontalButton.closable
      })
    </script>
  </body>
</html>
```

:::

## Examples

<op-horizontal-button
:title="title"
:description="description"
:closable="closable"
:styles="styles"
@click="onClick"
@close="onClose"
v-if="show" >

<div
        slot="icon"
        style="
          width: 70px;
          height: 70px;
          border-radius: 50%;
          background-color: #0033aa;
          display: flex;
          align-items: center;
          justify-content: center;
          overflow: hidden;
        "
      >
<svg
          width="50"
          height="50"
          viewBox="0 0 129 86"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
          style="margin-top: 10px"
        >
<path
            d="M98.0376 7.44807C97.8984 5.47056 96.6078 3.73894 94.6569 3.3038C84.6046 1.06103 74.3113 0 64 0C53.6887 0 43.3954 1.06103 33.3431 3.3038C31.3937 3.73894 30.1016 5.47056 29.9624 7.44807C29.3665 15.9393 27.3123 37.665 28.2346 46.417C30.3561 66.1325 44.4644 78.7367 61.4023 85.17C62.2378 85.4874 63.0687 85.769 63.9476 86C63.9626 85.9955 63.985 85.9881 64 85.9836C64.015 85.9881 64.0389 85.9955 64.0524 86C64.9313 85.769 65.7637 85.4933 66.5977 85.17C83.5805 78.853 97.6439 66.134 99.7654 46.417C100.688 37.665 98.6335 15.9393 98.0376 7.44807Z"
            fill="white"
          />
</svg>
</div>
</op-horizontal-button>
