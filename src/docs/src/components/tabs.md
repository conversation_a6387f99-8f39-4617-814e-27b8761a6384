---
outline: deep
---

<script setup lang="ts">
import 'onepulse-components/tabs'

</script>

# Tabs

The `op-tab-list` and `op-tab` components are used to create a tabbed interface. The `op-tab-list` component is a container for `op-tab` components and manages the state of the tabs. The `op-tab` component is a button that can be clicked to switch between tabs.

## Api

### Props

#### `op-tab-list`

| Name             | Type      | Default | Description                                                                                                                                                                                                                              |
| ---------------- | --------- | ------- | ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| `value`          | `string`  | `''`    | The value of the currently selected tab.                                                                                                                                                                                                 |
| `spaciousHeight` | `boolean` | `false` | Whether the tab list has more spacious height. When set to true, the height changes from `48px` to `64px`. This can be used for tab content that spans multiple lines. For example, having an icon on the first like and label below it. |

#### `op-tab`

| Name     | Type      | Default | Description                                                            |
| -------- | --------- | ------- | ---------------------------------------------------------------------- |
| `value`  | `string`  | `''`    | The value of the tab. This is used to determine which tab is selected. |
| `active` | `boolean` | `false` | Whether the tab is currently selected.                                 |

### Events

#### `op-tab-list`

| Name          | React Name      | Type                        | Description                                                                                                                     |
| ------------- | --------------- | --------------------------- | ------------------------------------------------------------------------------------------------------------------------------- |
| `updateValue` | `onupdateValue` | `OpTabListUpdateValueEvent` | Emitted when the selected tab changes. The new value is passed as the event payload. This is a `CustomEvent<{ value: string }>` |

#### `op-tab`

| Name           | React Name       | Type                     | Description                                                                                                                                      |
| -------------- | ---------------- | ------------------------ | ------------------------------------------------------------------------------------------------------------------------------------------------ |
| `updateActive` | `onupdateActive` | `OpTabUpdateActiveEvent` | Emitted when the tab is clicked. The new active state is passed as the event payload. This is a `CustomEvent<{ active: boolean, value: string}>` |

## Usage

::: code-group

```vue [Vue]
<script setup lang="ts">
import 'onepulse-components/tabs'
import type {
  OpTabListUpdateValueEvent,
  OpTabUpdateActiveEvent,
} from 'onepulse-components/tabs'
import { ref } from 'vue'

const selectedTab = ref('tab1')
const isTabHeightSpacious = ref(false)

const tabs = [
  { label: 'Tab 1', value: 'tab1' },
  { label: 'Tab 2', value: 'tab2' },
  { label: 'Tab 3', value: 'tab3' },
]

const onUpdateValue = (event: OpTabListUpdateValueEvent) => {
  selectedTab.value = event.detail.value
  isTabHeightSpacious.value = event.detail.value === 'tab2'
}

const onUpdateActive = (event: OpTabUpdateActiveEvent) => {
  console.log('Active tab:', event.detail)
}
</script>
<template>
  <main>
    <op-tab-list
      :value="selectedTab"
      @updateValue="onUpdateValue"
      :spaciousHeight="isTabHeightSpacious"
    >
      <op-tab
        v-for="tab in tabs"
        :key="tab.value"
        :value="tab.value"
        @updateActive="onUpdateActive"
      >
        {{ tab.label }}
      </op-tab>
    </op-tab-list>
    <div v-if="selectedTab === 'tab1'">Tab 1 content</div>
    <div v-else-if="selectedTab === 'tab2'">Tab 2 content</div>
    <div v-else-if="selectedTab === 'tab3'">Tab 3 content</div>
  </main>
</template>
```

```ts [Angular]
import { CommonModule } from '@angular/common'
import { Component, CUSTOM_ELEMENTS_SCHEMA } from '@angular/core'
import 'onepulse-components/tabs'

@Component({
  selector: 'app-root',
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  imports: [CommonModule],
  template: `
    <main>
      <op-tab-list
        [value]="selectedTab"
        (updateValue)="onUpdateValue($event)"
        [spaciousHeight]="isTabHeightSpacious"
      >
        <op-tab
          *ngFor="let tab of tabs"
          [key]="tab.value"
          [value]="tab.value"
          (updateActive)="onUpdateActive($event)"
        >
          {{ tab.label }}
        </op-tab>
      </op-tab-list>
      <div *ngIf="selectedTab === 'tab1'">Tab 1 content</div>
      <div *ngIf="selectedTab === 'tab2'">Tab 2 content</div>
      <div *ngIf="selectedTab === 'tab3'">Tab 3 content</div>
    </main>
  `,
  styleUrls: [],
})
export class AppComponent {
  selectedTab: string = 'tab1'
  isTabHeightSpacious: boolean = false

  tabs = [
    { label: 'Tab 1', value: 'tab1' },
    { label: 'Tab 2', value: 'tab2' },
    { label: 'Tab 3', value: 'tab3' },
  ]

  onUpdateValue(event: any) {
    this.selectedTab = event.detail.value
    this.isTabHeightSpacious = event.detail.value === 'tab2'
  }

  onUpdateActive(event: any) {
    console.log('Active tab:', event.detail)
  }
}
```

```tsx [React]
import { useState } from 'react'
import { OpTabList, OpTab } from 'onepulse-components/react/tabs'
import {
  OpTabListUpdateValueEvent,
  OpTabUpdateActiveEvent,
} from 'onepulse-components/tabs'

const App = () => {
  const [selectedTab, setSelectedTab] = useState('tab1')
  const [spaciousHeight, setSpaciousHeight] = useState(false)
  const tabs = [
    { value: 'tab1', label: 'Tab 1' },
    { value: 'tab2', label: 'Tab 2' },
    { value: 'tab3', label: 'Tab 3' },
  ]

  const onUpdateActive = (event: OpTabUpdateActiveEvent) => {
    console.log('Active tab:', event.detail)
  }

  const onUpdateValue = (event: OpTabListUpdateValueEvent) => {
    setSelectedTab(event.detail.value)
    setSpaciousHeight(event.detail.value === 'tab2')
  }

  return (
    <main>
      <OpTabList
        value={selectedTab}
        onupdateValue={onUpdateValue}
        spaciousHeight={spaciousHeight}
      >
        {tabs.map((tab) => (
          <OpTab
            key={tab.value}
            value={tab.value}
            onupdateActive={onUpdateActive}
          >
            {tab.label}
          </OpTab>
        ))}
      </OpTabList>
      {selectedTab === 'tab1' && <div>Tab 1 content</div>}
      {selectedTab === 'tab2' && <div>Tab 2 content</div>}
      {selectedTab === 'tab3' && <div>Tab 3 content</div>}
    </main>
  )
}

export default App
```

```html [No framework]
<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Document</title>
    <script type="module" src="/src/tabs.mts"></script>
  </head>

  <op-tab-list id="tab-list" spaciousHeight value="tab1">
    <op-tab value="tab1">Tab 1</op-tab>
    <op-tab value="tab2">Tab 2</op-tab>
    <op-tab value="tab3">Tab 3</op-tab>
  </op-tab-list>
  <div id="tab-content">
    <div id="tab1-content" style="display: none;">Tab 1 content</div>
    <div id="tab2-content" style="display: none;">Tab 2 content</div>
    <div id="tab3-content" style="display: none;">Tab 3 content</div>
  </div>

  <script>
    document.addEventListener('DOMContentLoaded', function() {
      const tabList = document.getElementById('tab-list');
      const tabContent = document.getElementById('tabContent');
      const tabs = ['tab1', 'tab2', 'tab3'];

      tabList.addEventListener('updateValue', function(event) {
        const selectedTab = event.detail.value;
        tabs.forEach(tab => {
          document.getElementById(`${tab}-content`).style.display = tab === selectedTab ? 'block' : 'none';
        });
        tabList.spaciousHeight = selectedTab === 'tab2';
      });
    });
  </script>
</body>
</html>
```

```html [LWC html]
<template lwc:if={componentsLoaded}>
        <op-tab-list lwc:external
        value={selectedTab}
        spaciousheight={isTabHeightSpacious}
      >
      <template for:each={tabs} for:item="tab">
        <op-tab lwc:external
          value={tab.value}
          key={tab.value}
        >
          { tab.label }
        </op-tab>
        </template>
      </op-tab-list>
      <template lwc:if={isTab1}>
        Tab 1 content
      </template>
        <template lwc:if={isTab2}>
            Tab 2 content
        </template>
        <template lwc:if={isTab3}>
            Tab 3 content
        </template>
    </template>
</template>
```

```js [LWC js]
import { LightningElement } from 'lwc'
import OSB_Components from '@salesforce/resourceUrl/OSB_Components'
import { loadScript } from 'lightning/platformResourceLoader'

export default class App extends LightningElement {
  componentsLoaded = false
  selectedTab = 'tab1'
  isTabHeightSpacious = false

  tabs = [
    { label: 'Tab 1', value: 'tab1' },
    { label: 'Tab 2', value: 'tab2' },
    { label: 'Tab 3', value: 'tab3' },
  ]

  onUpdateValue(event) {
    this.selectedTab = event.detail.value
    this.isTabHeightSpacious = event.detail.value === 'tab2'
  }

  async renderedCallback() {
    await loadScript(this, OSB_Components)
    setTimeout(() => {
      this.componentsLoaded = true
      this.template
        .querySelector('op-tab-list')
        .addEventListener('updateValue', this.onUpdateValue.bind(this))
    }, 10000)
  }

  get isTab1() {
    return this.selectedTab === 'tab1'
  }

  get isTab2() {
    return this.selectedTab === 'tab2'
  }

  get isTab3() {
    return this.selectedTab === 'tab3'
  }
}
```

:::

## Examples

### Basic

<op-tab-list value="tab1">
  <op-tab value="tab1">Tab 1</op-tab>
  <op-tab value="tab2">Tab 2</op-tab>
  <op-tab value="tab3">Tab 3</op-tab>
</op-tab-list>

### Spacious height

<op-tab-list value="tab1" spaciousHeight>
  <op-tab value="tab1">Tab 1</op-tab>
  <op-tab value="tab2">Tab 2</op-tab>
  <op-tab value="tab3">Tab 3</op-tab>
</op-tab-list>
