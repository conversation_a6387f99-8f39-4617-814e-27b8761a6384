---
outline: deep
---

<script setup lang="ts">
import 'onepulse-components/button'
</script>

# Button

The `op-button` component is a simple button component that can be used to trigger actions or navigate to different pages. It is a wrapper around the native `<button>` element and can be used in the same way.

## Api

### Props

| Name       | Type      | Default   | Description                                                       |
| ---------- | --------- | --------- | ----------------------------------------------------------------- |
| `type`     | `string`  | `button`  | The type of the button. Same as the native `<button>` element.    |
| `kind`     | `string`  | `primary` | The kind of button. Can be `primary`, `secondary`, or `tertiary`. |
| `disabled` | `boolean` | `false`   | Whether the button is disabled.                                   |
| `loading`  | `boolean` | `false`   | Whether the button is in a loading state.                         |
| `dense`    | `boolean` | `false`   | Whether the button has more dense/compact padding                 |
| `critical` | `boolean` | `false`   | Whether the button is critical state of the button kind           |
| `inverted` | `boolean` | `false`   | Whether the button is inverted state of the button kind           |

### Events

No custom events are emitted by the `op-button` component. The native `click` event is emitted when the button is clicked.

## Usage

::: code-group

```vue [Vue]
<script setup lang="ts">
import 'onepulse-components/button'
import { ref } from 'vue'

const isButtonCritical = ref(false)
const isButtonDisabled = ref(false)
const isButtonDense = ref(false)

const count = ref(0)

const onButtonClick = () => {
  count.value++
  isButtonCritical.value = count.value % 2 === 0
  isButtonDense.value = count.value % 4 === 0
}
</script>

<template>
  <main>
    <op-button
      @click="onButtonClick()"
      kind="tertiary"
      :critical="isButtonCritical"
      :disabled="isButtonDisabled"
      :dense="isButtonDense"
      >Click me</op-button
    >
  </main>
</template>
```

```ts [Angular]
import { Component, CUSTOM_ELEMENTS_SCHEMA } from '@angular/core'
import 'onepulse-components/button'

@Component({
  selector: 'app-root',
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  template: `
    <main>
      <op-button
        (click)="onButtonClick()"
        kind="tertiary"
        [critical]="isButtonCritical"
        [disabled]="isButtonDisabled"
        [dense]="isButtonDense"
      >
        Click me
      </op-button>
    </main>
  `,
  styles: [],
})
export class AppComponent {
  isButtonCritical = false
  isButtonDisabled = false
  isButtonDense = false
  count = 0

  onButtonClick() {
    this.count++
    this.isButtonCritical = this.count % 2 === 0
    this.isButtonDense = this.count % 4 === 0
  }
}
```

```tsx [React]
import { useState } from 'react'
import { OpButton } from 'onepulse-components/react/button'

const App = () => {
  const [isButtonCritical, setIsButtonCritical] = useState(false)
  const [isButtonDisabled] = useState(false)
  const [isButtonDense, setIsButtonDense] = useState(false)
  const [count, setCount] = useState(0)

  const onButtonClick = () => {
    const newCount = count + 1
    setCount(newCount)
    setIsButtonCritical(newCount % 2 === 0)
    setIsButtonDense(newCount % 4 === 0)
  }

  return (
    <main>
      <OpButton
        onClick={onButtonClick}
        kind="tertiary"
        critical={isButtonCritical}
        disabled={isButtonDisabled}
        dense={isButtonDense}
      >
        Click me
      </OpButton>
    </main>
  )
}

export default App
```

```html [No framework]
<!DOCTYPE html>

<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Document</title>
    <script type="module" src="/src/button.mts"></script>
  </head>
  <body>
    <main>
      <op-button
        kind="tertiary"
        onclick="onButtonClick()"
        id="button"
        critical
        dense
      >
        Click me
      </op-button>
    </main>
    <script>
      const button = document.getElementById('button')
      let isButtonCritical = false
      let isButtonDisabled = false
      let isButtonDense = false
      let count = 0

      function onButtonClick() {
        count++
        isButtonCritical = count % 2 === 0
        isButtonDense = count % 4 === 0

        button.critical = isButtonCritical
        button.disabled = isButtonDisabled
        button.dense = isButtonDense
      }
    </script>
  </body>
</html>
```

```html [LWC html]
<template>
  <template lwc:if="{componentsLoaded}">
    <op-button
      lwc:external
      kind="tertiary"
      onclick="{onButtonClick}"
      critical="{isButtonCritical}"
      disabled="{isButtonDisabled}"
      dense="{isButtonDense}"
    >
      Click me
    </op-button>
  </template>
</template>
```

```js [LWC js]
import { LightningElement } from 'lwc'
import OSB_Components from '@salesforce/resourceUrl/OSB_Components'
import { loadScript } from 'lightning/platformResourceLoader'

export default class App extends LightningElement {
  componentsLoaded = false
  isButtonCritical = false
  isButtonDisabled = false
  isButtonDense = false
  count = 0

  async renderedCallback() {
    await loadScript(this, OSB_Components)
    setTimeout(() => {
      this.componentsLoaded = true
    }, 10000)
  }

  onButtonClick() {
    this.count++
    this.isButtonCritical = this.count % 2 === 0
    this.isButtonDense = this.count % 4 === 0
  }
}
```

:::

## Examples

### Primary Button

| Default                        | Disabled                                | Loading                                | Dense                                | Critical                                | Inverted                                |
| ------------------------------ | --------------------------------------- | -------------------------------------- | ------------------------------------ | --------------------------------------- | --------------------------------------- |
| <op-button>Primary</op-button> | <op-button disabled>Primary</op-button> | <op-button loading>Primary</op-button> | <op-button dense>Primary</op-button> | <op-button critical>Primary</op-button> | <op-button inverted>Primary</op-button> |

### Secondary Button

| Default                                           | Disabled                                                   | Loading                                                   | Dense                                                   | Critical                                                   | Inverted                                                   |
| ------------------------------------------------- | ---------------------------------------------------------- | --------------------------------------------------------- | ------------------------------------------------------- | ---------------------------------------------------------- | ---------------------------------------------------------- |
| <op-button kind="secondary">Secondary</op-button> | <op-button kind="secondary" disabled>Secondary</op-button> | <op-button kind="secondary" loading>Secondary</op-button> | <op-button kind="secondary" dense>Secondary</op-button> | <op-button kind="secondary" critical>Secondary</op-button> | <op-button kind="secondary" inverted>Secondary</op-button> |

### Tertiary Button

| Default                                         | Disabled                                                 | Loading                                                 | Dense                                                 | Critical                                                 | Inverted                                                 |
| ----------------------------------------------- | -------------------------------------------------------- | ------------------------------------------------------- | ----------------------------------------------------- | -------------------------------------------------------- | -------------------------------------------------------- |
| <op-button kind="tertiary">Tertiary</op-button> | <op-button kind="tertiary" disabled>Tertiary</op-button> | <op-button kind="tertiary" loading>Tertiary</op-button> | <op-button kind="tertiary" dense>Tertiary</op-button> | <op-button kind="tertiary" critical>Tertiary</op-button> | <op-button kind="tertiary" inverted>Tertiary</op-button> |
