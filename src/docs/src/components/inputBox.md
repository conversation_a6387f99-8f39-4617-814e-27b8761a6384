---
outline: deep
---

<script setup lang="ts">
import 'onepulse-components/input'
</script>

# Input Box

The `op-input` component is a versatile form input field that can be used to collect user input. It features a Material Design-inspired style with floating labels, validation states, and customizable prefix and suffix slots.

## Api

### Props

| Name           | Type      | Default | Description                                          |
| -------------- | --------- | ------- | ---------------------------------------------------- |
| `value`        | `string`  | `''`    | The current value of the input field.                |
| `placeholder`  | `string`  | `''`    | Placeholder text displayed when the input is empty.  |
| `label`        | `string`  | `''`    | Label text displayed above the input field.          |
| `type`         | `string`  | `text`  | The type of input (text, email, password, etc.).     |
| `name`         | `string`  | `''`    | The name attribute of the input field.               |
| `disabled`     | `boolean` | `false` | Whether the input field is disabled.                 |
| `required`     | `boolean` | `false` | Whether the input field is required.                 |
| `helperText`   | `string`  | `''`    | Helper text displayed below the input field.         |
| `errorMessage` | `string`  | `''`    | Error message displayed when the input has an error. |
| `error`        | `boolean` | `false` | Whether the input field has an error state.          |

### Slots

| Name     | Description                                         |
| -------- | --------------------------------------------------- |
| `prefix` | Content to display at the start of the input field. |
| `suffix` | Content to display at the end of the input field.   |

### Events

| Name          | Detail              | Description                           |
| ------------- | ------------------- | ------------------------------------- |
| `updateValue` | `{ value: string }` | Emitted when the input value changes. |

## Usage

::: code-group

```vue [Vue]
<script setup lang="ts">
import 'onepulse-components/input'
import { ref } from 'vue'

const inputValue = ref('')
const searchValue = ref('')

const handleInputChange = (event) => {
  const customEvent = event as CustomEvent
  inputValue.value = customEvent.detail.value
}

const handleSearchChange = (event) => {
  const customEvent = event as CustomEvent
  searchValue.value = customEvent.detail.value
  // Perform search logic here
}
</script>

<template>
  <main>
    <!-- Standard input field -->
    <op-input
      label="Username"
      placeholder="Enter your username"
      name="username"
      required
      :value="inputValue"
      @updateValue="handleInputChange"
      helperText="Your username should be at least 3 characters long"
    ></op-input>

    <!-- Search input field -->
    <op-input
      label="Search"
      placeholder="Search..."
      name="search"
      :value="searchValue"
      @updateValue="handleSearchChange"
    >
      <template #prefix>
        <svg
          width="24"
          height="24"
          viewBox="0 0 24 24"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M15.5 14H14.71L14.43 13.73C15.41 12.59 16 11.11 16 9.5C16 5.91 13.09 3 9.5 3C5.91 3 3 5.91 3 9.5C3 13.09 5.91 16 9.5 16C11.11 16 12.59 15.41 13.73 14.43L14 14.71V15.5L19 20.49L20.49 19L15.5 14ZM9.5 14C7.01 14 5 11.99 5 9.5C5 7.01 7.01 5 9.5 5C11.99 5 14 7.01 14 9.5C14 11.99 11.99 14 9.5 14Z"
            fill="currentColor"
          />
        </svg>
      </template>
    </op-input>

    <!-- Input with custom slots -->
    <op-input
      label="Custom Slots"
      placeholder="Enter text..."
      name="custom-slots"
    >
      <template #prefix>
        <div style="color: blue;">@</div>
      </template>
      <template #suffix>
        <div style="color: green;">✓</div>
      </template>
    </op-input>

    <div>Input value: {{ inputValue }}</div>
    <div>Search value: {{ searchValue }}</div>
  </main>
</template>
```

```ts [Angular]
import { Component, CUSTOM_ELEMENTS_SCHEMA } from '@angular/core'
import 'onepulse-components/input'

@Component({
  selector: 'app-root',
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  template: `
    <main>
      <!-- Standard input field -->
      <op-input
        label="Username"
        placeholder="Enter your username"
        name="username"
        required
        [value]="inputValue"
        (updateValue)="handleInputChange($event)"
        helperText="Your username should be at least 3 characters long"
      ></op-input>

      <!-- Search input field -->
      <op-input
        label="Search"
        placeholder="Search..."
        name="search"
        [value]="searchValue"
        (updateValue)="handleSearchChange($event)"
      >
        <svg
          slot="prefix"
          width="24"
          height="24"
          viewBox="0 0 24 24"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M15.5 14H14.71L14.43 13.73C15.41 12.59 16 11.11 16 9.5C16 5.91 13.09 3 9.5 3C5.91 3 3 5.91 3 9.5C3 13.09 5.91 16 9.5 16C11.11 16 12.59 15.41 13.73 14.43L14 14.71V15.5L19 20.49L20.49 19L15.5 14ZM9.5 14C7.01 14 5 11.99 5 9.5C5 7.01 7.01 5 9.5 5C11.99 5 14 7.01 14 9.5C14 11.99 11.99 14 9.5 14Z"
            fill="currentColor"
          />
        </svg>
      </op-input>

      <div>Input value: {{ inputValue }}</div>
      <div>Search value: {{ searchValue }}</div>
    </main>
  `,
  styles: [],
})
export class AppComponent {
  inputValue = ''
  searchValue = ''

  handleInputChange(event: CustomEvent) {
    this.inputValue = event.detail.value
  }

  handleSearchChange(event: CustomEvent) {
    this.searchValue = event.detail.value
    // Perform search logic here
  }
}
```

```tsx [React]
import { useState } from 'react'
import { OpInput } from 'onepulse-components/react/input'

const App = () => {
  const [inputValue, setInputValue] = useState('')
  const [searchValue, setSearchValue] = useState('')

  const handleInputChange = (e) => {
    setInputValue(e.detail.value)
  }

  const handleSearchChange = (e) => {
    setSearchValue(e.detail.value)
    // Perform search logic here
  }

  return (
    <main>
      {/* Standard input field */}
      <OpInput
        label="Username"
        placeholder="Enter your username"
        name="username"
        required
        value={inputValue}
        onupdateValue={handleInputChange}
        helperText="Your username should be at least 3 characters long"
      />

      {/* Search input field */}
      <OpInput
        label="Search"
        placeholder="Search..."
        name="search"
        value={searchValue}
        onupdateValue={handleSearchChange}
      >
        <svg
          slot="prefix"
          width="24"
          height="24"
          viewBox="0 0 24 24"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M15.5 14H14.71L14.43 13.73C15.41 12.59 16 11.11 16 9.5C16 5.91 13.09 3 9.5 3C5.91 3 3 5.91 3 9.5C3 13.09 5.91 16 9.5 16C11.11 16 12.59 15.41 13.73 14.43L14 14.71V15.5L19 20.49L20.49 19L15.5 14ZM9.5 14C7.01 14 5 11.99 5 9.5C5 7.01 7.01 5 9.5 5C11.99 5 14 7.01 14 9.5C14 11.99 11.99 14 9.5 14Z"
            fill="currentColor"
          />
        </svg>
      </OpInput>

      {/* Input with custom slots */}
      <OpInput
        label="Custom Slots"
        placeholder="Enter text..."
        name="custom-slots"
      >
        <div slot="prefix" style={{ color: 'blue' }}>
          @
        </div>
        <div slot="suffix" style={{ color: 'green' }}>
          ✓
        </div>
      </OpInput>

      <div>Input value: {inputValue}</div>
      <div>Search value: {searchValue}</div>
    </main>
  )
}

export default App
```

```html [No framework]
<!DOCTYPE html>

<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Document</title>
    <script type="module" src="/src/input.mts"></script>
  </head>
  <body>
    <main>
      <!-- Standard input field -->
      <op-input
        id="username-input"
        label="Username"
        placeholder="Enter your username"
        name="username"
        required
        helperText="Your username should be at least 3 characters long"
      ></op-input>

      <!-- Search input field -->
      <op-input
        id="search-input"
        label="Search"
        placeholder="Search..."
        name="search"
      >
        <svg
          slot="prefix"
          width="24"
          height="24"
          viewBox="0 0 24 24"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M15.5 14H14.71L14.43 13.73C15.41 12.59 16 11.11 16 9.5C16 5.91 13.09 3 9.5 3C5.91 3 3 5.91 3 9.5C3 13.09 5.91 16 9.5 16C11.11 16 12.59 15.41 13.73 14.43L14 14.71V15.5L19 20.49L20.49 19L15.5 14ZM9.5 14C7.01 14 5 11.99 5 9.5C5 7.01 7.01 5 9.5 5C11.99 5 14 7.01 14 9.5C14 11.99 11.99 14 9.5 14Z"
            fill="currentColor"
          />
        </svg>
      </op-input>

      <div id="input-value-display">Input value:</div>
      <div id="search-value-display">Search value:</div>
    </main>
    <script>
      const usernameInput = document.getElementById('username-input')
      const searchInput = document.getElementById('search-input')
      const inputValueDisplay = document.getElementById('input-value-display')
      const searchValueDisplay = document.getElementById('search-value-display')

      usernameInput.addEventListener('updateValue', (e) => {
        inputValueDisplay.textContent = `Input value: ${e.detail.value}`
      })

      searchInput.addEventListener('updateValue', (e) => {
        searchValueDisplay.textContent = `Search value: ${e.detail.value}`
        // Perform search logic here
      })
    </script>
  </body>
</html>
```

```html [LWC html]
<template>
  <template lwc:if="{componentsLoaded}">
    <!-- Standard input field -->
    <op-input
      lwc:external
      label="Username"
      placeholder="Enter your username"
      name="username"
      required
      value="{inputValue}"
      onupdateValue="{handleInputChange}"
      helperText="Your username should be at least 3 characters long"
    ></op-input>

    <!-- Search input field -->
    <op-input
      lwc:external
      label="Search"
      placeholder="Search..."
      name="search"
      value="{searchValue}"
      onupdateValue="{handleSearchChange}"
    >
      <svg
        slot="prefix"
        width="24"
        height="24"
        viewBox="0 0 24 24"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M15.5 14H14.71L14.43 13.73C15.41 12.59 16 11.11 16 9.5C16 5.91 13.09 3 9.5 3C5.91 3 3 5.91 3 9.5C3 13.09 5.91 16 9.5 16C11.11 16 12.59 15.41 13.73 14.43L14 14.71V15.5L19 20.49L20.49 19L15.5 14ZM9.5 14C7.01 14 5 11.99 5 9.5C5 7.01 7.01 5 9.5 5C11.99 5 14 7.01 14 9.5C14 11.99 11.99 14 9.5 14Z"
          fill="currentColor"
        />
      </svg>
    </op-input>

    <div>Input value: {inputValue}</div>
    <div>Search value: {searchValue}</div>
  </template>
</template>
```

```js [LWC js]
import { LightningElement } from 'lwc'
import OnePulseComponents from '@salesforce/resourceUrl/OnePulseComponents'
import { loadScript } from 'lightning/platformResourceLoader'

export default class App extends LightningElement {
  componentsLoaded = false
  inputValue = ''
  searchValue = ''

  async renderedCallback() {
    if (this.componentsLoaded) {
      return
    }

    try {
      await loadScript(this, OnePulseComponents + '/input.js')
      this.componentsLoaded = true
    } catch (error) {
      console.error('Error loading OnePulse components', error)
    }
  }

  handleInputChange(event) {
    const customEvent = event
    this.inputValue = customEvent.detail.value
  }

  handleSearchChange(event) {
    const customEvent = event
    this.searchValue = customEvent.detail.value
    // Perform search logic here
  }
}
```

:::

## Examples

### Input Fields

<div style="margin-top: 24px;"></div>

<div style="display: flex; flex-wrap: wrap; gap: 16px; margin-bottom: 24px;">
  <op-input
    label="Empty"
    placeholder="Enter text here"
    name="mat-input-1"
    style="width: 200px;"
  ></op-input>

  <op-input
    label="Populated"
    placeholder="Enter text here"
    name="mat-input-2"
    value="Populated"
    style="width: 200px;"
  ></op-input>

  <op-input
    label="Required"
    placeholder="Enter text here"
    name="mat-input-3"
    required
    style="width: 200px;"
  ></op-input>

  <op-input
    label="Error"
    placeholder="Enter text here"
    name="mat-input-4"
    style="width: 200px;"
    error
  ></op-input>

  <op-input
    label="Disabled"
    placeholder="Enter text here"
    name="mat-input-6"
    value="Populated"
    style="width: 200px;"
    disabled
  ></op-input>

</div>

### Input Field with Helper Text

<div style="margin-top: 24px;"></div>

<op-input
  label="Username"
  placeholder="Enter your username"
  name="username"
  required
  helperText="Your username should be at least 3 characters long"
></op-input>

### Search Input Field

<div style="margin-top: 24px;"></div>

<op-input
label="Search"
placeholder="Search..."
name="search"
>
  <div slot="prefix">
    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M15.5 14H14.71L14.43 13.73C15.41 12.59 16 11.11 16 9.5C16 5.91 13.09 3 9.5 3C5.91 3 3 5.91 3 9.5C3 13.09 5.91 16 9.5 16C11.11 16 12.59 15.41 13.73 14.43L14 14.71V15.5L19 20.49L20.49 19L15.5 14ZM9.5 14C7.01 14 5 11.99 5 9.5C5 7.01 7.01 5 9.5 5C11.99 5 14 7.01 14 9.5C14 11.99 11.99 14 9.5 14Z" fill="currentColor"/>
    </svg>
  </div>
</op-input>

### Input Field with Custom Prefix and Suffix

<div style="margin-top: 24px;"></div>

<op-input
label="Custom Slots"
placeholder="Enter text..."
name="custom-slots"
>
  <div slot="prefix" style="color: blue;">@</div>
  <div slot="suffix" style="color: green;">✓</div>
</op-input>
