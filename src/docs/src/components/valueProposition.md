---
outline: deep
---

<script setup lang="ts">
import 'onepulse-components/value-proposition'
</script>

# Value Proposition

The `op-value-proposition` component is a card-like UI element designed to highlight key features or benefits. It features a shield icon that sits half in and half out of the top of the card, with the ability to add custom icons inside the shield.

## Api

### Props

| Name        | Type              | Default | Description                                                  |
| ----------- | ----------------- | ------- | ------------------------------------------------------------ |
| title       | string            | ''      | The title text displayed in the value proposition card       |
| description | string            | ''      | The description text displayed in the value proposition card |
| variant     | 'white' \| 'blue' | 'white' | The color variant of the card                                |

### Slots

| Name | Description                                   |
| ---- | --------------------------------------------- |
| icon | Custom icon to be displayed inside the shield |

### CSS Custom Properties

The component uses variables for colors defined in the constants file.

## Usage

### Basic Usage

```html
<op-value-proposition
  title="Default Shield"
  description="This is the default shield icon without any custom icon inside."
  variant="white"
></op-value-proposition>
```

### Framework Examples

::: code-group

```vue [Vue]
<script setup lang="ts">
import 'onepulse-components/value-proposition'
</script>

<template>
  <main>
    <op-value-proposition
      title="Default Shield"
      description="This is the default shield icon without any custom icon inside."
      variant="white"
    ></op-value-proposition>

    <op-value-proposition
      title="Custom Icon"
      description="This example uses a custom icon inside the shield."
      variant="white"
    >
      <svg
        slot="icon"
        xmlns="http://www.w3.org/2000/svg"
        width="24"
        height="24"
        viewBox="0 0 24 24"
        fill="none"
        stroke="currentColor"
        stroke-width="2"
        stroke-linecap="round"
        stroke-linejoin="round"
      >
        <path d="M12 2L2 7l10 5 10-5-10-5z"></path>
        <path d="M2 17l10 5 10-5"></path>
        <path d="M2 12l10 5 10-5"></path>
      </svg>
    </op-value-proposition>
  </main>
</template>
```

```ts [Angular]
import { Component, CUSTOM_ELEMENTS_SCHEMA } from '@angular/core'
import 'onepulse-components/value-proposition'

@Component({
  selector: 'app-root',
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  template: `
    <main>
      <op-value-proposition
        title="Default Shield"
        description="This is the default shield icon without any custom icon inside."
        variant="white"
      ></op-value-proposition>

      <op-value-proposition
        title="Custom Icon"
        description="This example uses a custom icon inside the shield."
        variant="white"
      >
        <svg
          slot="icon"
          xmlns="http://www.w3.org/2000/svg"
          width="24"
          height="24"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          stroke-width="2"
          stroke-linecap="round"
          stroke-linejoin="round"
        >
          <path d="M12 2L2 7l10 5 10-5-10-5z"></path>
          <path d="M2 17l10 5 10-5"></path>
          <path d="M2 12l10 5 10-5"></path>
        </svg>
      </op-value-proposition>
    </main>
  `,
  styles: [],
})
export class AppComponent {}
```

```tsx [React]
import { OpValueProposition } from 'onepulse-components/react/value-proposition'

function App() {
  return (
    <main>
      <OpValueProposition
        title="Default Shield"
        description="This is the default shield icon without any custom icon inside."
        variant="white"
      />

      <OpValueProposition
        title="Custom Icon"
        description="This example uses a custom icon inside the shield."
        variant="white"
      >
        <svg
          slot="icon"
          xmlns="http://www.w3.org/2000/svg"
          width="24"
          height="24"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          stroke-width="2"
          stroke-linecap="round"
          stroke-linejoin="round"
        >
          <path d="M12 2L2 7l10 5 10-5-10-5z"></path>
          <path d="M2 17l10 5 10-5"></path>
          <path d="M2 12l10 5 10-5"></path>
        </svg>
      </OpValueProposition>
    </main>
  )
}

export default App
```

```html [No framework]
<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Value Proposition Example</title>
    <script type="module" src="/src/value-proposition.mts"></script>
  </head>
  <body>
    <op-value-proposition
      title="Default Shield"
      description="This is the default shield icon without any custom icon inside."
      variant="white"
    ></op-value-proposition>

    <op-value-proposition
      title="Custom Icon"
      description="This example uses a custom icon inside the shield."
      variant="white"
    >
      <svg
        slot="icon"
        xmlns="http://www.w3.org/2000/svg"
        width="24"
        height="24"
        viewBox="0 0 24 24"
        fill="none"
        stroke="currentColor"
        stroke-width="2"
        stroke-linecap="round"
        stroke-linejoin="round"
      >
        <path d="M12 2L2 7l10 5 10-5-10-5z"></path>
        <path d="M2 17l10 5 10-5"></path>
        <path d="M2 12l10 5 10-5"></path>
      </svg>
    </op-value-proposition>
  </body>
</html>
```

:::

## Examples

### Default Shield (White Variant)

<div style="margin-bottom: 50px"></div>
<op-value-proposition
  title="Default Shield"
  description="This is the default shield icon without any custom icon inside."
  variant="white"
></op-value-proposition>

### Custom Icon (White Variant)

<div style="margin-bottom: 50px"></div>
<op-value-proposition
  title="Custom Icon"
  description="This example uses a custom icon inside the shield."
  variant="white"
>
  <svg slot="icon" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
    <path d="M12 2L2 7l10 5 10-5-10-5z"></path>
    <path d="M2 17l10 5 10-5"></path>
    <path d="M2 12l10 5 10-5"></path>
  </svg>
</op-value-proposition>

### Blue Variant with Custom Icon

<div style="margin-bottom: 50px"></div>
<op-value-proposition
  title="Blue Variant"
  description="This is an example with a blue background and a custom icon."
  variant="blue"
>
  <svg slot="icon" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
    <circle cx="12" cy="12" r="10"></circle>
    <line x1="12" y1="8" x2="12" y2="12"></line>
    <line x1="12" y1="16" x2="12.01" y2="16"></line>
  </svg>
</op-value-proposition>
