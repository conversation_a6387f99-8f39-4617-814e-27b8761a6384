---
outline: deep
---

<script setup lang="ts">
import 'onepulse-components/loader'
import { ref } from 'vue'

const label = ref('Loading...')
</script>

# Loader

The `op-loader` component is a simple loader component that can be used to indicate that a process is running in the background.

## Api

### Props

| Name    | Type     | Default | Description                            |
| ------- | -------- | ------- | -------------------------------------- |
| `label` | `string` | `''`    | The label to display below the loader. |

### Events

No custom events are emitted by the `op-loader` component.

### Slots

No custom slots are provided by the `op-loader` component.

## Usage

::: code-group

```vue [Vue]
<script setup lang="ts">
import 'onepulse-components/loader'
import { ref } from 'vue'

const label = ref('Loading...')
</script>

<template>
  <main>
    <op-loader :label="label" />
  </main>
</template>
```

```ts [Angular]
import { Component, CUSTOM_ELEMENTS_SCHEMA } from '@angular/core'
import 'onepulse-components/loader'

@Component({
  selector: 'app-root',
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  template: `
    <main>
      <op-loader [label]="label" />
    </main>
  `,
  styles: [],
})
export class AppComponent {
  label = 'Loading...'
}
```

```tsx [React]
import { OpLoader } from 'onepulse-components/react/loader'

function App() {
  const label = 'Loading...'

  return (
    <main>
      <OpLoader label={label} />
    </main>
  )
}

export default App
```

:::

## Examples

| Custom Label                 | No Label      |
| ---------------------------- | ------------- |
| <op-loader :label="label" /> | <op-loader /> |
