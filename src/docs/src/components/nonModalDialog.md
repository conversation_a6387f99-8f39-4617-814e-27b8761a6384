---
outline: deep
---

<script setup lang="ts">
import 'onepulse-components/button'
import '../../../components/src/non-modal-dialog.mjs'
import { ref } from 'vue'
import { type OpNonModalDialogUpdateVisibleEvent } from '../../../components/src/non-modal-dialog.mjs'

const isDialogVisible = ref(false)

const onOpenDialog = () => {
  isDialogVisible.value = true
}

const onUpdatedVisible = (event: OpNonModalDialogUpdateVisibleEvent) => {
  isDialogVisible.value = event.detail.visible
}

const onReset = () => {
  console.log('Reset filter')
}

const onApply = () => {
  console.log('Apply filter')
  isDialogVisible.value = false
}
</script>

# Non-Modal Dialog

The `op-non-modal-dialog` component is a dialog that doesn't block interaction with the rest of the page. It can be positioned in different locations on the screen and is useful for notifications, panels, and other non-blocking UI elements.

## Api

### Props

| Name       | Type        | Default    | Description                                                                                                                                                                           |
| ---------- | ----------- | ---------- | ------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| `visible`  | `boolean`   | `false`    | Whether the dialog is visible. When set to `true`, the dialog is displayed.                                                                                                           |
| `classes`  | `ClassInfo` | `{}`       | Additional classes to add to the dialog. For example, `{foo: bar}` applies the class `foo` if the value of `bar` is truthy.                                                           |
| `styles`   | `StyleInfo` | `{}`       | Additional styles to add to the dialog. For example, `{backgroundColor: 'red', 'border-top': '5px', '--size': '0'}` sets the `background-color`, `border-top` and `--size` properties |
| `position` | `string`    | `'center'` | The position of the dialog. Can be `'center'`, `'top-right'`, `'top-left'`, `'bottom-right'`, or `'bottom-left'`.                                                                     |
| `movable`  | `boolean`   | `true`     | Whether the dialog can be moved by dragging its header. When set to `true`, the dialog header will show a drag handle and can be dragged to reposition the dialog.                    |

### Events

| Name            | React Name        | Type                                 | Description                                                                                                                                                                                                                                                                              |
| --------------- | ----------------- | ------------------------------------ | ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| `updateVisible` | `onupdateVisible` | `OpNonModalDialogUpdateVisibleEvent` | Emitted when the dialog visibility changes. The new visibility state is passed as the event payload. This is a `CustomEvent<{ visible: boolean }>`. The event is only emitted when dialog is closed by user interaction, such as clicking the close icon, instead of direct state change |
| `move`          | `onmove`          | `OpNonModalDialogMoveEvent`          | Emitted when the dialog is moved by dragging. The new position coordinates are passed as the event payload. This is a `CustomEvent<{ x: number, y: number }>`. The event is emitted continuously during dragging and once when dragging ends.                                            |

### Slots

| Name      | Description                                                                      |
| --------- | -------------------------------------------------------------------------------- |
| `header`  | The header content of the dialog. This is displayed at the top of the dialog.    |
| `content` | The main content of the dialog. This is displayed in the middle of the dialog.   |
| `footer`  | The footer content of the dialog. This is displayed at the bottom of the dialog. |

## Usage

::: code-group

```vue [Vue]
<script setup lang="ts">
import 'onepulse-components/button'
import 'onepulse-components/non-modal-dialog'
import { ref } from 'vue'
import { type OpNonModalDialogUpdateVisibleEvent } from 'onepulse-components/non-modal-dialog'

const isDialogVisible = ref(false)

const onOpenDialog = () => {
  isDialogVisible.value = true
}

const onUpdatedVisible = (event: OpNonModalDialogUpdateVisibleEvent) => {
  isDialogVisible.value = event.detail.visible
}

const onClose = () => {
  isDialogVisible.value = false
}
</script>

<template>
  <main>
    <op-button @click="onOpenDialog">Open Non-Modal Dialog</op-button>

    <op-non-modal-dialog
      :styles="{ width: '400px' }"
      :visible="isDialogVisible"
      position="top-right"
      @updateVisible="onUpdatedVisible"
    >
      <span slot="header">Notification</span>
      <div slot="content">
        <p>
          This is a non-modal dialog that doesn't block interaction with the
          rest of the page.
        </p>
      </div>
      <div slot="footer" style="display: flex; justify-content: flex-end">
        <op-button @click="onClose">Close</op-button>
      </div>
    </op-non-modal-dialog>
  </main>
</template>
```

```ts [Angular]
import { CommonModule } from '@angular/common'
import { Component, CUSTOM_ELEMENTS_SCHEMA } from '@angular/core'
import 'onepulse-components/button'
import 'onepulse-components/non-modal-dialog'
import { OpNonModalDialogUpdateVisibleEvent } from 'onepulse-components/non-modal-dialog'

@Component({
  selector: 'app-root',
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  imports: [CommonModule],
  template: `
    <main>
      <op-button (click)="onOpenDialog()">Open Non-Modal Dialog</op-button>

      <op-non-modal-dialog
        [styles]="{ width: '400px' }"
        [visible]="isDialogVisible"
        position="top-right"
        (updateVisible)="onUpdatedVisible($event)"
      >
        <span slot="header">Notification</span>
        <div slot="content">
          <p>
            This is a non-modal dialog that doesn't block interaction with the
            rest of the page.
          </p>
        </div>
        <div slot="footer" style="display: flex; justify-content: flex-end">
          <op-button (click)="onClose()">Close</op-button>
        </div>
      </op-non-modal-dialog>
    </main>
  `,
  styleUrls: [],
})
export class AppComponent {
  isDialogVisible = false

  onOpenDialog() {
    this.isDialogVisible = true
  }

  onUpdatedVisible(event: Event) {
    const customEvent = event as OpNonModalDialogUpdateVisibleEvent
    this.isDialogVisible = customEvent.detail.visible
  }

  onClose() {
    this.isDialogVisible = false
  }
}
```

```tsx [React]
import { useState } from 'react'
import { OpButton } from 'onepulse-components/react/button'
import { OpNonModalDialog } from 'onepulse-components/react/non-modal-dialog'
import { OpNonModalDialogUpdateVisibleEvent } from 'onepulse-components/non-modal-dialog'

function App() {
  const [isDialogVisible, setIsDialogVisible] = useState(false)

  const onOpenDialog = () => {
    setIsDialogVisible(true)
  }

  const onUpdatedVisible = (event: OpNonModalDialogUpdateVisibleEvent) => {
    setIsDialogVisible(event.detail.visible)
  }

  const onClose = () => {
    setIsDialogVisible(false)
  }

  return (
    <main>
      <OpButton onClick={onOpenDialog}>Open Non-Modal Dialog</OpButton>

      <OpNonModalDialog
        styles={{ width: '400px' }}
        visible={isDialogVisible}
        position="top-right"
        onupdateVisible={onUpdatedVisible}
      >
        <span slot="header">Notification</span>
        <div slot="content">
          <p>
            This is a non-modal dialog that doesn't block interaction with the
            rest of the page.
          </p>
        </div>
        <div
          slot="footer"
          style={{ display: 'flex', justifyContent: 'flex-end' }}
        >
          <OpButton onClick={onClose}>Close</OpButton>
        </div>
      </OpNonModalDialog>
    </main>
  )
}

export default App
```

```html [No framework]
<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Document</title>
    <script type="module" src="/src/index.mts"></script>
  </head>
  <body>
    <op-button id="open-dialog">Open Non-Modal Dialog</op-button>

    <op-non-modal-dialog
      id="dialog"
      styles='{"width":"400px"}'
      position="top-right"
    >
      <span slot="header">Notification</span>
      <div slot="content">
        <p>
          This is a non-modal dialog that doesn't block interaction with the
          rest of the page.
        </p>
      </div>
      <div slot="footer" style="display: flex; justify-content: flex-end;">
        <op-button id="close-button">Close</op-button>
      </div>
    </op-non-modal-dialog>

    <script>
      document.addEventListener('DOMContentLoaded', function () {
        const openButton = document.getElementById('open-dialog')
        const dialog = document.getElementById('dialog')
        const closeButton = document.getElementById('close-button')

        openButton.addEventListener('click', function () {
          dialog.visible = true
        })

        dialog.addEventListener('updateVisible', function (e) {
          console.log(e.detail.visible)
        })

        closeButton.addEventListener('click', function () {
          dialog.visible = false
        })
      })
    </script>
  </body>
</html>
```

```html [LWC html]
<template>
  <template lwc:if="{componentsLoaded}">
    <op-button lwc:external onclick="{onOpenDialog}"
      >Open Non-Modal Dialog</op-button
    >
    <op-non-modal-dialog
      lwc:external
      styles="{dialogStyles}"
      visible="{isDialogVisible}"
      position="top-right"
    >
      <span slot="header">Notification</span>
      <div slot="content">
        <p>
          This is a non-modal dialog that doesn't block interaction with the
          rest of the page.
        </p>
      </div>
      <div slot="footer" style="display: flex; justify-content: flex-end">
        <op-button lwc:external onclick="{onClose}">Close</op-button>
      </div>
    </op-non-modal-dialog>
  </template>
</template>
```

```js [LWC js]
import { LightningElement } from 'lwc'
import OSB_Components from '@salesforce/resourceUrl/OSB_Components'
import { loadScript } from 'lightning/platformResourceLoader'

export default class App extends LightningElement {
  isDialogVisible = false
  componentsLoaded = false
  dialogStyles = { width: '400px' }

  async renderedCallback() {
    if (this.componentsLoaded) {
      return
    }

    try {
      await loadScript(this, OSB_Components)
      this.componentsLoaded = true
      this.template
        .querySelector('op-non-modal-dialog')
        .addEventListener('updateVisible', this.onUpdatedVisible.bind(this))
    } catch (error) {
      console.error('Error loading OnePulse components', error)
    }
  }

  onOpenDialog() {
    this.isDialogVisible = true
  }

  onUpdatedVisible(event) {
    this.isDialogVisible = event.detail.visible
  }

  onClose() {
    this.isDialogVisible = false
  }
}
```

:::

## Examples

### Key Features

#### Different Positions

The non-modal dialog can be positioned in different locations on the screen:

- **center**: Centred in the viewport (default)
- **top-right**: Fixed in the top-right corner
- **top-left**: Fixed in the top-left corner
- **bottom-right**: Fixed in the bottom-right corner
- **bottom-left**: Fixed in the bottom-left corner

```html
<op-non-modal-dialog position="top-right" styles='{"width":"300px"}'>
  <span slot="header">Notification</span>
  <div slot="content">
    <p>This is a notification positioned in the top-right corner.</p>
  </div>
  <div slot="footer">
    <op-button>Dismiss</op-button>
  </div>
</op-non-modal-dialog>
```

#### Movable Dialog

The non-modal dialog can be moved by dragging its header:

```html
<op-non-modal-dialog movable styles='{"width":"400px"}'>
  <span slot="header">Movable Dialog</span>
  <div slot="content">
    <p>Drag the header to reposition this dialog.</p>
  </div>
  <div slot="footer">
    <op-button>Close</op-button>
  </div>
</op-non-modal-dialog>
```

You can track when the dialog is moved using the `move` event:

```javascript
dialog.addEventListener('move', (e) => {
  console.log(`Position: x=${e.detail.x}, y=${e.detail.y}`)
})
```

### Live Example

<op-button @click="onOpenDialog">Open Dialog</op-button>

<op-non-modal-dialog
:styles="{ width: '360px' }"
:visible="isDialogVisible"
position="center"
movable
@updateVisible="onUpdatedVisible">
<span slot="header">Filter</span>

  <div slot="content" style="display: flex; flex-direction: column; gap: 16px;">
    <label style="display: flex; align-items: center;">
      <input type="checkbox" style="margin-right: 12px;"> My APIs
    </label>
    <label style="display: flex; align-items: center;">
      <input type="checkbox" checked style="margin-right: 12px;"> Standard Bank APIs
    </label>
    <label style="display: flex; align-items: center;">
      <input type="checkbox" style="margin-right: 12px;"> Partner APIs
    </label>
  </div>
  <div slot="footer" style="display: flex; justify-content: space-between;">
    <op-button kind="tertiary" @click="onReset">Reset Filter</op-button>
    <op-button @click="onApply">Apply Filter</op-button>
  </div>
</op-non-modal-dialog>

### Use Cases

1. **Notifications**: Display non-blocking notifications in the corner of the screen
2. **Quick Settings**: Show settings panels that don't interrupt the user's workflow
3. **Contextual Help**: Provide help information without blocking the UI
4. **Previews**: Show previews of content while allowing interaction with the main UI
5. **Toolbars**: Create floating toolbars or panels for specific actions
6. **Movable Panels**: Create panels that users can position wherever they want on the screen
7. **Filter Dialog**: Create filter panels for data filtering and selection
