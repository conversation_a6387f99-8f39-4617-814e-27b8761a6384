---
outline: deep
---

<script setup lang="ts">
import 'onepulse-components/card'
import 'onepulse-components/button'
</script>

# Card

The `op-card` component is a versatile container that can be used to display content with a consistent layout. It features a header with title and description props, content area, and footer, with an optional icon slot in the header.

## Api

### Props

| Name        | Type    | Default | Description                                                 |
| ----------- | ------- | ------- | ----------------------------------------------------------- |
| title       | string  | ''      | The title text displayed in the card header                 |
| description | string  | ''      | The description/subtitle text displayed in the card header  |
| elevated    | boolean | false   | Whether the card has elevated appearance with deeper shadow |
| styles      | object  | {}      | Custom styles to apply to the card container                |
| classes     | object  | {}      | Custom classes to apply to the card container               |

### Slots

| Name    | Description                                            |
| ------- | ------------------------------------------------------ |
| icon    | Optional icon displayed at the left side of the header |
| default | Main content of the card                               |
| footer  | Content for the card footer                            |

## Usage

::: code-group

```vue [Vue]
<script setup lang="ts">
import 'onepulse-components/card'
import 'onepulse-components/button'
</script>

<template>
  <main>
    <op-card title="SBG Streaming" description="Streaming Service" elevated>
      <div
        slot="icon"
        style="width: 70px; height: 70px; border-radius: 50%; background-color: #0033AA; display: flex; align-items: center; justify-content: center;"
      >
        <!-- Icon content -->
      </div>
      <p>SBG Streaming offers a wide variety of content.</p>
      <div slot="footer">
        <op-button>Action</op-button>
        <op-button kind="secondary">Action</op-button>
      </div>
    </op-card>
  </main>
</template>
```

```ts [Angular]
import { Component, CUSTOM_ELEMENTS_SCHEMA } from '@angular/core'
import 'onepulse-components/card'
import 'onepulse-components/button'

@Component({
  selector: 'app-root',
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  template: `
    <main>
      <op-card title="SBG Streaming" description="Streaming Service" elevated>
        <div
          slot="icon"
          style="width: 70px; height: 70px; border-radius: 50%; background-color: #0033AA; display: flex; align-items: center; justify-content: center;"
        >
          <!-- Icon content -->
        </div>
        <p>SBG Streaming offers a wide variety of content.</p>
        <div slot="footer">
          <op-button>Action</op-button>
          <op-button kind="secondary">Action</op-button>
        </div>
      </op-card>
    </main>
  `,
  styles: [],
})
export class AppComponent {}
```

```tsx [React]
import { OpCard } from 'onepulse-components/react/card'
import { OpButton } from 'onepulse-components/react/button'

function App() {
  return (
    <main>
      <OpCard title="SBG Streaming" description="Streaming Service" elevated>
        <div
          slot="icon"
          style={{
            width: '70px',
            height: '70px',
            borderRadius: '50%',
            backgroundColor: '#0033AA',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
          }}
        >
          {/* Icon content */}
        </div>
        <p>SBG Streaming offers a wide variety of content.</p>
        <div slot="footer">
          <OpButton>Action</OpButton>
          <OpButton kind="secondary">Action</OpButton>
        </div>
      </OpCard>
    </main>
  )
}

export default App
```

```html [LWC html]
<template>
  <template lwc:if="{componentsLoaded}">
    <main>
      <op-card
        lwc:external
        title="SBG Streaming"
        description="Streaming Service"
        elevated
      >
        <div
          slot="icon"
          style="width: 70px; height: 70px; border-radius: 50%; background-color: #0033AA; display: flex; align-items: center; justify-content: center;"
        >
          <!-- Icon content -->
        </div>
        <p>SBG Streaming offers a wide variety of content.</p>
        <div slot="footer">
          <op-button lwc:external>Action</op-button>
          <op-button lwc:external kind="secondary">Action</op-button>
        </div>
      </op-card>
    </main>
  </template>
</template>
```

```js [LWC js]
import { LightningElement } from 'lwc'
import OSB_Components from '@salesforce/resourceUrl/OSB_Components'
import { loadScript } from 'lightning/platformResourceLoader'

export default class App extends LightningElement {
  componentsLoaded = false

  async renderedCallback() {
    await loadScript(this, OSB_Components)
    setTimeout(() => {
      this.componentsLoaded = true
    }, 10000)
  }
}
```

:::

## Examples

### Basic Card

<div style="max-width: 500px; margin-bottom: 24px;">
  <op-card title="Basic Card" description="Simple card without icon">
    <p>This is a basic card with header and footer but no icon.</p>
    <div slot="footer">
      <op-button>Action</op-button>
    </div>
  </op-card>
</div>

### Elevated Card with Icon

<div style="max-width: 500px;">
  <op-card title="SBG Streaming" description="Streaming Service" elevated>
    <div
      slot="icon"
      style="width: 70px; height: 70px; border-radius: 50%; background-color: #0033AA; display: flex; align-items: center; justify-content: center; overflow: hidden;"
    >
      <svg
        width="50"
        height="50"
        viewBox="0 0 129 86"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
        style="margin-top: 10px;"
      >
        <path
          d="M98.0376 7.44807C97.8984 5.47056 96.6078 3.73894 94.6569 3.3038C84.6046 1.06103 74.3113 0 64 0C53.6887 0 43.3954 1.06103 33.3431 3.3038C31.3937 3.73894 30.1016 5.47056 29.9624 7.44807C29.3665 15.9393 27.3123 37.665 28.2346 46.417C30.3561 66.1325 44.4644 78.7367 61.4023 85.17C62.2378 85.4874 63.0687 85.769 63.9476 86C63.9626 85.9955 63.985 85.9881 64 85.9836C64.015 85.9881 64.0389 85.9955 64.0524 86C64.9313 85.769 65.7637 85.4933 66.5977 85.17C83.5805 78.853 97.6439 66.134 99.7654 46.417C100.688 37.665 98.6335 15.9393 98.0376 7.44807Z"
          fill="white"
        />
      </svg>
    </div>
    <p>SBG Streaming offers a wide variety of award-winning TV shows, movies, and documentaries on thousands of internet-connected devices.</p>
    <div slot="footer">
      <op-button>Primary Action</op-button>
      <op-button kind="secondary">Secondary Action</op-button>
    </div>
  </op-card>
</div>
