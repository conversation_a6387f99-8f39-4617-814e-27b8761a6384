---
outline: deep
---

<script setup lang="ts">
import 'onepulse-components/checkbox'
</script>

# Checkbox

The `op-checkbox` component is a simple checkbox input that allows users to select or deselect an option. It is a wrapper around the native `<input type="checkbox">` element and can be used in the same way.

## Api

### Props

| Name            | Type      | Default | Description                                     |
| --------------- | --------- | ------- | ----------------------------------------------- |
| `name`          | `string`  | `''`    | The name of the checkbox.                       |
| `checked`       | `boolean` | `false` | Whether the checkbox is checked.                |
| `disabled`      | `boolean` | `false` | Whether the checkbox is disabled.               |
| `indeterminate` | `boolean` | `false` | Whether the checkbox is in indeterminate state. |
| `value`         | `string`  | `''`    | The value of the checkbox.                      |

### Events

| Name            | React Name        | Type                     | Description                                        |
| --------------- | ----------------- | ------------------------ | -------------------------------------------------- |
| `updateChecked` | `onupdateChecked` | `OpCheckboxCheckedEvent` | Emitted when the checkbox is checked or unchecked. |

### Slots

Default slot is used to display the label of the checkbox.

## Usage

::: code-group

```vue [Vue]
<script setup lang="ts">
import 'onepulse-components/checkbox'
import type { OpCheckboxCheckedEvent } from 'onepulse-components/checkbox'
import { ref } from 'vue'

const isError = ref(false)
const isDisabled = ref(false)
const isIndeterminate = ref(true)
const isChecked = ref(false)

const count = ref(0)

const onUpdateChecked = (event: OpCheckboxCheckedEvent) => {
  count.value++
  isError.value = count.value % 5 === 0
  isIndeterminate.value = count.value % 3 === 0
  isChecked.value = event.detail.checked
  isDisabled.value = count.value > 20
}
</script>

<template>
  <main>
    <op-checkbox
      @updateChecked="onUpdateChecked"
      :disabled="isDisabled"
      :indeterminate="isIndeterminate"
      name="error-checkbox"
      :error="isError"
      value="error"
      >Label</op-checkbox
    >
  </main>
</template>
```

```ts [Angular]
import { Component, CUSTOM_ELEMENTS_SCHEMA } from '@angular/core'
import 'onepulse-components/checkbox'
import { OpCheckboxCheckedEvent } from 'onepulse-components/checkbox'

@Component({
  selector: 'app-root',
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  template: `
    <main>
      <op-checkbox
        (updateChecked)="onUpdateChecked($event)"
        [disabled]="isDisabled"
        [indeterminate]="isIndeterminate"
        name="error-checkbox"
        [error]="isError"
        value="error"
        >Label</op-checkbox
      >
    </main>
  `,
  styles: [],
})
export class AppComponent {
  isError = false
  isDisabled = false
  isIndeterminate = true
  isChecked = false

  count = 0

  onUpdateChecked(event: Event) {
    const checkboxEvent = event as OpCheckboxCheckedEvent
    this.count++
    this.isError = this.count % 5 === 0
    this.isIndeterminate = this.count % 3 === 0
    this.isChecked = checkboxEvent.detail.checked
    this.isDisabled = this.count > 20
  }
}
```

```tsx [React]
import { useState } from 'react'
import { OpCheckboxCheckedEvent } from 'onepulse-components/checkbox'
import { OpCheckbox } from 'onepulse-components/react/checkbox'

const App = () => {
  const [isDisabled, setIsDisabled] = useState(false)
  const [isError, setIsError] = useState(false)
  const [isIndeterminate, setIsIndeterminate] = useState(true)
  const [isChecked, setIsChecked] = useState(false)
  const [count, setCount] = useState(0)

  const onUpdateChecked = (event: OpCheckboxCheckedEvent) => {
    const newCount = count + 1
    setCount(newCount)
    setIsError(newCount % 5 === 0)
    setIsIndeterminate(newCount % 3 === 0)
    setIsChecked(event.detail.checked)
    setIsDisabled(newCount > 20)
  }

  return (
    <main>
      <OpCheckbox
        onupdateChecked={onUpdateChecked}
        checked={isChecked}
        disabled={isDisabled}
        indeterminate={isIndeterminate}
        name="error-checkbox"
        error={isError}
        value="error"
      >
        Label
      </OpCheckbox>
    </main>
  )
}

export default App
```

```html [No framework]
<!DOCTYPE html>

<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Document</title>
    <script type="module" src="/src/index.mts"></script>
  </head>
  <body>
    <main>
      <op-checkbox
        onupdateChecked="onUpdateChecked"
        name="error-checkbox"
        error
        value="error"
        >Label</op-checkbox
      >
    </main>
    <script>
      let isError = false
      let isDisabled = false
      let isIndeterminate = true
      let isChecked = false

      let count = 0

      const onUpdateChecked = (event) => {
        count++
        isError = count % 5 === 0
        isIndeterminate = count % 3 === 0
        isChecked = event.detail.checked
        isDisabled = count > 20
      }
      const checkbox = document.querySelector('op-checkbox')
      checkbox.addEventListener('updateChecked', onUpdateChecked)
      checkbox.setAttribute('error', isError)
      checkbox.setAttribute('indeterminate', isIndeterminate)
      checkbox.setAttribute('checked', isChecked)
      checkbox.setAttribute('disabled', isDisabled)
    </script>
  </body>
</html>
```

:::

## Examples

### Default

| Default                           | Checked                                   | Indeterminate                                           |
| --------------------------------- | ----------------------------------------- | ------------------------------------------------------- |
| <op-checkbox> label</op-checkbox> | <op-checkbox checked> label</op-checkbox> | <op-checkbox checked indeterminate> label</op-checkbox> |

### Error

| Default                                 | Checked                                         | Indeterminate                                                 |
| --------------------------------------- | ----------------------------------------------- | ------------------------------------------------------------- |
| <op-checkbox error> label</op-checkbox> | <op-checkbox checked error> label</op-checkbox> | <op-checkbox checked indeterminate error> label</op-checkbox> |

### Disabled

| Default                                    | Checked                                            | Indeterminate                                                    |
| ------------------------------------------ | -------------------------------------------------- | ---------------------------------------------------------------- |
| <op-checkbox disabled> label</op-checkbox> | <op-checkbox checked disabled> label</op-checkbox> | <op-checkbox checked indeterminate disabled> label</op-checkbox> |
