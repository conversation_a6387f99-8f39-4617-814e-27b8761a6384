---
outline: deep
---

<script setup lang="ts">
import 'onepulse-components/detailed-product-card'
import 'onepulse-components/button'
</script>

# Detailed Product Card

The `op-detailed-product-card` component is a horizontal card designed to showcase products or services with a prominent visual and flexible actions. It features a blue gradient background with image and content areas.

## Api

### Props

| Name        | Type   | Default | Description                                   |
| ----------- | ------ | ------- | --------------------------------------------- |
| title       | string | ''      | The product title displayed prominently       |
| description | string | ''      | The product description text                  |
| styles      | object | {}      | Custom styles to apply to the card container  |
| classes     | object | {}      | Custom classes to apply to the card container |

### Slots

| Name    | Description                                         |
| ------- | --------------------------------------------------- |
| image   | Content for the left side image area                |
| actions | Content for the actions area (buttons, links, etc.) |

## Usage

::: code-group

```vue [Vue]
<script setup lang="ts">
import 'onepulse-components/detailed-product-card'
import 'onepulse-components/button'
</script>

<template>
  <main>
    <op-detailed-product-card
      title="Global Trading Platform"
      description="Access international markets with our advanced trading technology"
    >
      <img
        slot="image"
        src="/trading-platform.jpg"
        alt="Trading Platform"
        style="width: 100%; height: 100%; object-fit: cover;"
      />
      <op-button slot="actions">START TRADING</op-button>
    </op-detailed-product-card>
  </main>
</template>
```

```ts [Angular]
import { Component, CUSTOM_ELEMENTS_SCHEMA } from '@angular/core'
import 'onepulse-components/detailed-product-card'
import 'onepulse-components/button'

@Component({
  selector: 'app-root',
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  template: `
    <main>
      <op-detailed-product-card
        title="Digital Banking Solutions"
        description="Transform your banking experience with our secure platform"
      >
        <img
          slot="image"
          src="/banking-app.jpg"
          alt="Banking App"
          style="width: 100%; height: 100%; object-fit: cover;"
        />
        <op-button slot="actions">EXPLORE FEATURES</op-button>
      </op-detailed-product-card>
    </main>
  `,
  styles: [],
})
export class AppComponent {}
```

```tsx [React]
import { OpDetailedProductCard } from 'onepulse-components/react/detailed-product-card'
import { OpButton } from 'onepulse-components/react/button'

function App() {
  return (
    <main>
      <OpDetailedProductCard
        title="Investment Portfolio Manager"
        description="Manage your investments with intelligent insights"
      >
        <img
          slot="image"
          src="/portfolio-dashboard.jpg"
          alt="Portfolio Dashboard"
          style={{
            width: '100%',
            height: '100%',
            objectFit: 'cover',
          }}
        />
        <OpButton slot="actions">GET STARTED</OpButton>
      </OpDetailedProductCard>
    </main>
  )
}

export default App
```

```html [LWC html]
<template>
  <template lwc:if="{componentsLoaded}">
    <main>
      <op-detailed-product-card
        lwc:external
        title="Investment Portfolio Manager"
        description="Manage your investments with intelligent insights"
      >
        <img
          slot="image"
          src="/portfolio-dashboard.jpg"
          alt="Portfolio Dashboard"
          style="width: 100%; height: 100%; object-fit: cover;"
        />
        <op-button lwc:external slot="actions">GET STARTED</op-button>
      </op-detailed-product-card>
    </main>
  </template>
</template>
```

```js [LWC js]
import { LightningElement } from 'lwc'
import OSB_Components from '@salesforce/resourceUrl/OSB_Components'
import { loadScript } from 'lightning/platformResourceLoader'

export default class App extends LightningElement {
  componentsLoaded = false

  async renderedCallback() {
    await loadScript(this, OSB_Components)
    setTimeout(() => {
      this.componentsLoaded = true
    }, 10000)
  }
}
```

:::

## Examples

### Detailed Product Card

<div style="max-width: 800px; margin-bottom: 24px;">
  <op-detailed-product-card
    title="Premium Banking Services"
    description="Experience personalized banking with our premium services">
    <div
      slot="image"
      style="width: 100%; height: 100%; background: linear-gradient(45deg, #1e40af, #3b82f6); display: flex; align-items: center; justify-content: center; color: white; font-size: 18px; font-weight: bold;">
      Banking product image goes here
    </div>
    <op-button slot="actions">LEARN MORE</op-button>
  </op-detailed-product-card>
</div>
