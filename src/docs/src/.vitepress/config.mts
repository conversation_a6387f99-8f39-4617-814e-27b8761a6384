import { defineConfig } from 'vitepress'

// https://vitepress.dev/reference/site-config
export default defineConfig({
  title: 'Onepulse Components',
  description: 'Documentation site for Onepulse UI components ',
  themeConfig: {
    // https://vitepress.dev/reference/default-theme-config
    nav: [
      { text: 'Guide', link: '/guide' },
      { text: 'Components', link: '/components' },
      {
        text: 'About',
        items: [
          {
            text: 'Changelog',
            link: 'https://github.com/standardbank-cibbt/onepulse-components/blob/main/src/components/CHANGELOG.md',
          },
          {
            text: 'Releases',
            link: 'https://github.com/standardbank-cibbt/onepulse-components/releases',
          },
          { text: 'Contributing', link: '/contributing' },
        ],
      },
    ],

    sidebar: {
      '/components/': [
        {
          text: 'Components',
          base: '/components',
          items: [
            { text: 'Button', link: '/button' },
            { text: 'Card', link: '/card' },
            { text: 'Checkbox', link: '/checkbox' },
            { text: 'Detailed Product Card', link: '/detailedProductCard' },
            { text: 'Dialog', link: '/dialog' },
            { text: 'Horizontal button', link: '/horizontal-button' },
            { text: 'Input Box', link: '/inputBox' },
            { text: 'Loader', link: '/loader' },
            { text: 'Non-Modal Dialog', link: '/nonModalDialog' },
            { text: 'Tabs', link: '/tabs' },
            { text: 'Value Proposition', link: '/valueProposition' },
          ],
        },
      ],
    },

    socialLinks: [
      {
        icon: 'github',
        link: 'https://github.com/standardbank-cibbt/onepulse-components',
      },
    ],
    search: {
      provider: 'local',
    },
    lastUpdated: {
      text: 'Last updated',
      formatOptions: {
        dateStyle: 'full',
        timeStyle: 'medium',
      },
    },
  },
  vue: {
    template: {
      compilerOptions: {
        isCustomElement: (tag) => tag.startsWith('op-'),
      },
    },
  },
})
