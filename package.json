{"name": "onepulse-components", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "spell-check": "cspell .", "prettier": "prettier --write .", "prettier:check": "prettier --check .", "prepare": "husky", "commit": "git-cz", "commitlint": "commitlint --edit", "install:components": "cd src/components && npm ci", "install:docs": "cd src/docs && npm ci", "build:components": "cd src/components && npm run build", "bundle:components": "cd src/components && npm run build:bundled", "build:docs": "cd src/docs && npm run build"}, "keywords": ["onepulse"], "author": "<EMAIL>", "license": "ISC", "devDependencies": {"@commitlint/cli": "^19.6.1", "@commitlint/config-conventional": "^19.6.0", "commitizen": "^4.3.1", "cspell": "^8.17.2", "cz-conventional-changelog": "^3.3.0", "husky": "^9.1.7", "prettier": "3.4.2"}}