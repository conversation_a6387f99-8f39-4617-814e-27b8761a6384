name: Components release

on:
  release:
    types: [published]

jobs:
  release:
    name: Release components
    runs-on: ubuntu-latest

    steps:
      - name: Check out code
        uses: actions/checkout@v4

      - name: Use nodejs
        uses: actions/setup-node@v4
        with:
          node-version: 22.x
          cache: 'npm'
          cache-dependency-path: '**/package-lock.json'

      - name: Install dependencies
        run: npm run install:components

      - id: set-version
        uses: standardbank-cibbt/onepulse-package-version@v3.0.1
        with:
          token: ${{ secrets.GITHUB_TOKEN }}
          base-path: src/components

      - name: Build components
        run: npm run build:components

      - name: Package components
        run: |
          cd src/components
          npm pack
          mv onepulse-components-*.tgz onepulse-components-${{ steps.set-version.outputs.package-version }}.tgz

      - name: Upload components package to actions
        uses: actions/upload-artifact@v4
        with:
          name: 'onepulse-components-${{ steps.set-version.outputs.package-version }}.tgz'
          path: 'src/components/onepulse-components-${{ steps.set-version.outputs.package-version }}.tgz'

      - name: Bundle components
        run: |
          cd src/components
          npm run build:bundled
          mv bundle.js onepulse-components-${{ steps.set-version.outputs.package-version }}.js

      - name: Upload components bundle to actions
        uses: actions/upload-artifact@v4
        with:
          name: 'onepulse-components-${{ steps.set-version.outputs.package-version }}.js'
          path: 'src/components/onepulse-components-${{ steps.set-version.outputs.package-version }}.js'
